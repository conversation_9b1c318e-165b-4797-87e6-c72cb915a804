#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光效资源提取工具
根据光效title名称从游戏客户端中提取相关的光效资源文件
"""

import os
import sys
import shutil
import configparser
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from pathlib import Path
import logging
import time
import re


class EffectExtractor:
    def __init__(self):
        self.logger = self._setup_logger()
        self._ini_cache = {}  # INI文件缓存
        self._mapping_cache = {}  # 资源映射缓存
        self._effect_index = {}  # 光效配置索引缓存
        self._initialized = False  # 初始化标志
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('EffectExtractor')
        logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        return logger
    
    def parse_ini_file(self, file_path, encoding='gbk'):
        """解析INI文件"""
        try:
            # 首先尝试标准解析
            config = configparser.ConfigParser()
            config.read(file_path, encoding=encoding)
            return config
        except configparser.DuplicateOptionError as e:
            # 静默处理重复键名，不显示警告
            # 尝试使用允许重复键名的解析器
            try:
                config = configparser.ConfigParser(allow_no_value=True, strict=False)
                config.read(file_path, encoding=encoding)
                return config
            except Exception as e2:
                # 最后尝试手动解析
                return self._manual_parse_ini(file_path, encoding)
        except Exception as e:
            self.logger.error(f"解析INI文件失败 {file_path}: {e}")
            # 尝试手动解析
            return self._manual_parse_ini(file_path, encoding)

    def _manual_parse_ini(self, file_path, encoding='gbk'):
        """手动解析有问题的INI文件"""
        try:
            config = configparser.ConfigParser()

            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()

            current_section = None
            processed_lines = []
            seen_keys = {}  # 跟踪每个section中已见过的键

            for line_num, line in enumerate(lines, 1):
                line = line.strip()

                # 跳过空行和注释
                if not line or line.startswith('#') or line.startswith(';'):
                    processed_lines.append(line)
                    continue

                # 检查是否是section头
                if line.startswith('[') and line.endswith(']'):
                    current_section = line[1:-1]
                    seen_keys[current_section] = set()
                    processed_lines.append(line)
                    continue

                # 检查是否是键值对
                if '=' in line and current_section:
                    key, _ = line.split('=', 1)
                    key = key.strip().lower()

                    # 如果键已经存在，跳过这一行
                    if key in seen_keys[current_section]:
                        continue

                    seen_keys[current_section].add(key)
                    processed_lines.append(line)
                else:
                    processed_lines.append(line)

            # 使用处理后的内容创建临时文件并解析
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', encoding=encoding, delete=False, suffix='.ini') as temp_file:
                temp_file.write('\n'.join(processed_lines))
                temp_path = temp_file.name

            try:
                config.read(temp_path, encoding=encoding)
                os.unlink(temp_path)  # 删除临时文件
                return config
            except Exception as e:
                os.unlink(temp_path)  # 删除临时文件
                raise e

        except Exception as e:
            self.logger.error(f"手动解析INI文件失败 {file_path}: {e}")
            return None
    
    def _build_effect_index(self, client_path):
        """构建光效配置索引 - 一次性解析所有光效"""
        if self._initialized:
            return

        ini_dir = os.path.join(client_path, 'ini')
        self.logger.info("构建光效配置索引...")

        start_time = time.time()

        for ini_file in ['3deffect2.ini', '3deffect3.ini']:
            ini_path = os.path.join(ini_dir, ini_file)
            if os.path.exists(ini_path):
                self._index_effect_file(ini_path, ini_file)

        build_time = time.time() - start_time
        self.logger.info(f"光效索引构建完成: {len(self._effect_index)} 个光效, 耗时 {build_time:.3f}秒")
        self._initialized = True

    def _index_effect_file(self, ini_path, ini_file):
        """索引单个光效文件 - 使用快速字符串搜索"""
        try:
            with open(ini_path, 'r', encoding='gbk') as f:
                content = f.read()

            # 使用正则表达式快速查找所有section
            import re
            section_pattern = re.compile(r'^\[([^\]]+)\]', re.MULTILINE)
            sections = section_pattern.findall(content)

            # 为每个section建立索引
            for section_name in sections:
                if section_name not in self._effect_index:
                    self._effect_index[section_name] = {
                        'file': ini_file,
                        'path': ini_path
                    }

            self.logger.info(f"索引文件 {ini_file}: {len(sections)} 个光效")

        except Exception as e:
            self.logger.error(f"索引光效文件失败 {ini_path}: {e}")

    def find_effect_config(self, client_path, effect_title):
        """在3deffect2.ini和3deffect3.ini中查找光效配置 - 优化版本"""
        # 确保索引已构建
        self._build_effect_index(client_path)

        # 从索引中快速查找
        if effect_title in self._effect_index:
            effect_info = self._effect_index[effect_title]
            ini_path = effect_info['path']
            ini_file = effect_info['file']

            # 使用缓存的配置解析器
            config = self._get_cached_config(ini_path)
            if config and effect_title in config.sections():
                effect_config = config[effect_title]
                self.logger.info(f"在 {ini_file} 中找到光效配置: {effect_title}")
                return effect_config, ini_file

        return None, None

    def _get_cached_config(self, ini_path):
        """获取缓存的配置对象"""
        if ini_path not in self._ini_cache:
            config = self.parse_ini_file(ini_path)
            if config:
                self._ini_cache[ini_path] = config

        return self._ini_cache.get(ini_path)
    
    def parse_effect_params(self, effect_config):
        """解析光效参数"""
        params = {
            'amount': 0,
            'effect_ids': [],
            'texture_ids': [],
            'texture_ids_1': [],  # 新增：支持TextureId_1
            'texture_ids_2': [],  # 新增：支持TextureId_2
            'texture_ids_3': [],  # 新增：支持TextureId_3
            'texture_ids_4': [],  # 新增：支持TextureId_4
            'mix_opts': [],       # 新增：支持MixOpt
            'asb_values': [],
            'adb_values': [],
            'ztest_values': [],
            'lev_values': [],
            'delay': 0,
            'loop_time': 99999999,
            'frame_interval': 33,
            'loop_interval': 0,
            'offset_x': 0,
            'offset_y': 0,
            'offset_z': 0,
            'manual_uv_step': 0
        }
        
        try:
            # 解析基本参数
            params['amount'] = effect_config.getint('Amount', 0)
            params['delay'] = effect_config.getint('Delay', 0)
            params['loop_time'] = effect_config.getint('LoopTime', 99999999)
            params['frame_interval'] = effect_config.getint('FrameInterval', 33)
            params['loop_interval'] = effect_config.getint('LoopInterval', 0)
            params['offset_x'] = effect_config.getint('OffsetX', 0)
            params['offset_y'] = effect_config.getint('OffsetY', 0)
            params['offset_z'] = effect_config.getint('OffsetZ', 0)
            params['manual_uv_step'] = effect_config.getint('ManualUVStep', 0)
            
            # 解析EffectId和TextureId等数组参数
            for i in range(params['amount']):
                effect_id = effect_config.getint(f'EffectId{i}', 0)
                texture_id = effect_config.getint(f'TextureId{i}', 0)

                # 解析多张辅助贴图
                texture_id_1 = effect_config.getint(f'TextureId{i}_1', 0)
                texture_id_2 = effect_config.getint(f'TextureId{i}_2', 0)
                texture_id_3 = effect_config.getint(f'TextureId{i}_3', 0)
                texture_id_4 = effect_config.getint(f'TextureId{i}_4', 0)

                mix_opt = effect_config.getint(f'MixOpt{i}', 0)  # 新增：支持MixOpt
                asb = effect_config.getint(f'ASB{i}', 0)
                adb = effect_config.getint(f'ADB{i}', 0)
                ztest = effect_config.getint(f'ZTest{i}', 0)
                lev = effect_config.getint(f'Lev{i}', 0)

                params['effect_ids'].append(effect_id)
                params['texture_ids'].append(texture_id)

                # 添加辅助贴图ID（只有当存在时才添加）
                if texture_id_1 > 0:
                    params['texture_ids_1'].append(texture_id_1)
                if texture_id_2 > 0:
                    params['texture_ids_2'].append(texture_id_2)
                if texture_id_3 > 0:
                    params['texture_ids_3'].append(texture_id_3)
                if texture_id_4 > 0:
                    params['texture_ids_4'].append(texture_id_4)

                if mix_opt > 0:  # 只有当MixOpt存在时才添加
                    params['mix_opts'].append(mix_opt)
                params['asb_values'].append(asb)
                params['adb_values'].append(adb)
                params['ztest_values'].append(ztest)
                params['lev_values'].append(lev)
            
            self.logger.info(f"解析到 {params['amount']} 个光效资源")
            return params
            
        except Exception as e:
            self.logger.error(f"解析光效参数失败: {e}")
            return None
    
    def get_resource_paths(self, client_path, effect_ids, texture_ids, texture_ids_1=None, texture_ids_2=None, texture_ids_3=None, texture_ids_4=None):
        """获取模型和贴图资源路径 - 优化版本，支持多张辅助贴图"""
        ini_dir = os.path.join(client_path, 'ini')

        # 解析3deffectobj.ini获取模型路径
        effect_obj_ini = os.path.join(ini_dir, '3deffectobj.ini')
        texture_ini = os.path.join(ini_dir, '3dtexture.ini')

        model_paths = []
        texture_paths = []

        # 获取模型路径
        if os.path.exists(effect_obj_ini):
            model_paths = self._parse_resource_mapping_cached(effect_obj_ini, effect_ids, "模型")

        # 获取贴图路径 - 包括主贴图和多张辅助贴图
        if os.path.exists(texture_ini):
            # 合并所有贴图ID，一次性解析
            all_texture_ids = list(texture_ids)

            # 添加所有辅助贴图ID
            aux_texture_lists = [texture_ids_1, texture_ids_2, texture_ids_3, texture_ids_4]
            for aux_list in aux_texture_lists:
                if aux_list:
                    all_texture_ids.extend(aux_list)

            all_texture_paths = self._parse_resource_mapping_cached(texture_ini, all_texture_ids, "贴图")

            # 分离主贴图和辅助贴图
            texture_paths = all_texture_paths[:len(texture_ids)]

            # 添加所有辅助贴图路径
            current_index = len(texture_ids)
            for aux_list in aux_texture_lists:
                if aux_list:
                    aux_count = len(aux_list)
                    texture_paths.extend(all_texture_paths[current_index:current_index + aux_count])
                    current_index += aux_count

        return model_paths, texture_paths

    def _parse_resource_mapping(self, ini_file, resource_ids, resource_type):
        """解析资源映射文件 - 优化版本"""
        paths = []

        # 如果没有资源ID，直接返回
        if not resource_ids:
            return paths

        # 过滤掉无效ID，转换为字符串集合用于快速查找
        valid_ids = {str(rid) for rid in resource_ids if rid > 0}
        if not valid_ids:
            return paths

        try:
            # 一次性读取文件并解析
            with open(ini_file, 'r', encoding='gbk') as f:
                content = f.read()

            # 快速解析映射关系
            mapping = {}
            for line in content.split('\n'):
                line = line.strip()
                if line and '=' in line and not line.startswith('//'):
                    eq_pos = line.find('=')
                    if eq_pos > 0:
                        key = line[:eq_pos].strip()
                        if key in valid_ids:  # 只处理需要的ID
                            value = line[eq_pos+1:].strip()
                            mapping[key] = value

            # 按原始顺序返回路径
            for resource_id in resource_ids:
                if resource_id == 0:
                    continue
                str_id = str(resource_id)
                if str_id in mapping:
                    paths.append(mapping[str_id])
                    self.logger.info(f"找到{resource_type}路径: {resource_id} -> {mapping[str_id]}")
                else:
                    self.logger.warning(f"未找到{resource_type}Id {resource_id} 对应的路径")

        except Exception as e:
            self.logger.error(f"解析{resource_type}映射文件失败 {ini_file}: {e}")

        return paths

    def _parse_resource_mapping_cached(self, ini_file, resource_ids, resource_type):
        """解析资源映射文件 - 高性能版本"""
        # 检查缓存
        if ini_file not in self._mapping_cache:
            self._load_mapping_file_optimized(ini_file, resource_type)

        mapping = self._mapping_cache.get(ini_file, {})

        # 批量查找路径
        paths = []
        for resource_id in resource_ids:
            if resource_id == 0:
                continue
            str_id = str(resource_id)
            if str_id in mapping:
                paths.append(mapping[str_id])
                self.logger.debug(f"找到{resource_type}路径: {resource_id} -> {mapping[str_id]}")
            else:
                self.logger.warning(f"未找到{resource_type}Id {resource_id} 对应的路径")

        return paths

    def _load_mapping_file_optimized(self, ini_file, resource_type):
        """优化的映射文件加载"""
        try:
            start_time = time.time()

            # 使用更高效的文件读取
            with open(ini_file, 'r', encoding='gbk', buffering=8192) as f:
                content = f.read()

            # 使用正则表达式批量解析
            mapping = {}
            pattern = re.compile(r'^(\d+)=(.+)$', re.MULTILINE)
            matches = pattern.findall(content)

            for key, value in matches:
                mapping[key] = value.strip()

            # 缓存映射
            self._mapping_cache[ini_file] = mapping

            load_time = time.time() - start_time
            self.logger.info(f"缓存{resource_type}映射文件: {ini_file} ({len(mapping)} 条记录, {load_time:.3f}秒)")

        except Exception as e:
            self.logger.error(f"解析{resource_type}映射文件失败 {ini_file}: {e}")
            self._mapping_cache[ini_file] = {}

    def copy_resources(self, client_path, output_path, effect_title, model_paths, texture_paths, effect_config=None):
        """复制资源文件到输出目录"""
        # 创建基础输出目录 - 简化层级，直接在客户端更新下创建光效目录
        base_output_dir = os.path.join(output_path, '客户端更新', effect_title)
        os.makedirs(base_output_dir, exist_ok=True)

        copied_files = []

        # 高性能文件复制 - 并行处理，支持按帧重命名
        all_paths = model_paths + texture_paths

        if all_paths:
            copied_files.extend(self._copy_files_parallel(client_path, base_output_dir, all_paths, model_paths, effect_config))

        # 保存光效配置到ini文件
        if effect_config:
            self._save_effect_config(base_output_dir, effect_title, effect_config)

        return copied_files

    def _copy_files_parallel(self, client_path, output_dir, all_paths, model_paths, params=None):
        """并行复制文件 - 高性能版本，支持按帧重命名"""
        from concurrent.futures import ThreadPoolExecutor
        import shutil

        copied_files = []

        # 生成重命名映射
        rename_mapping = self._generate_rename_mapping(all_paths, model_paths, params)

        # 预检查存在的文件
        existing_files = []
        for file_path in all_paths:
            src_path = os.path.join(client_path, file_path)
            if os.path.exists(src_path):
                # 使用重命名后的文件名
                new_filename = rename_mapping.get(file_path, os.path.basename(file_path))
                dst_path = os.path.join(output_dir, new_filename)
                file_type = "模型文件" if file_path in model_paths else "贴图文件"
                existing_files.append((src_path, dst_path, file_type, new_filename))
            else:
                file_type = "模型文件" if file_path in model_paths else "贴图文件"
                self.logger.warning(f"{file_type}不存在: {src_path}")

        if not existing_files:
            return copied_files

        # 并行复制文件
        def copy_single_file(file_info):
            src_path, dst_path, file_type, filename = file_info
            try:
                shutil.copy2(src_path, dst_path)
                self.logger.info(f"复制{file_type}: {filename}")
                return dst_path
            except Exception as e:
                self.logger.error(f"复制文件失败 {src_path}: {e}")
                return None

        # 使用线程池并行处理
        max_workers = min(4, len(existing_files))  # 限制线程数
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(executor.map(copy_single_file, existing_files))

        # 收集成功复制的文件
        copied_files = [result for result in results if result is not None]

        return copied_files

    def _generate_rename_mapping(self, all_paths, model_paths, effect_config):
        """生成文件重命名映射 - 按帧顺序命名"""
        rename_mapping = {}

        if not effect_config:
            # 如果没有配置信息，使用原文件名
            return rename_mapping

        try:
            # 从光效配置中解析参数
            params = self.parse_effect_params(effect_config)
            if not params:
                return rename_mapping

            # 获取光效参数
            texture_ids = params.get('texture_ids', [])
            texture_ids_1 = params.get('texture_ids_1', [])
            texture_ids_2 = params.get('texture_ids_2', [])
            texture_ids_3 = params.get('texture_ids_3', [])
            texture_ids_4 = params.get('texture_ids_4', [])

            # 收集所有辅助贴图ID
            all_aux_texture_ids = []
            aux_texture_mapping = {}  # 记录每个ID对应的辅助贴图序号

            for aux_num, aux_ids in enumerate([texture_ids_1, texture_ids_2, texture_ids_3, texture_ids_4], 1):
                for texture_id in aux_ids:
                    all_aux_texture_ids.append(texture_id)
                    aux_texture_mapping[texture_id] = aux_num

            # 按帧顺序重命名模型文件
            for i, model_path in enumerate([p for p in all_paths if p in model_paths]):
                frame_num = i + 1  # 从1开始编号
                file_ext = os.path.splitext(model_path)[1]  # 获取文件扩展名
                new_name = f"{frame_num}{file_ext}"
                rename_mapping[model_path] = new_name
                self.logger.debug(f"模型重命名: {os.path.basename(model_path)} -> {new_name}")

            # 按帧顺序重命名主贴图文件
            texture_paths = [p for p in all_paths if p not in model_paths]
            main_texture_count = len(texture_ids)

            for i, texture_path in enumerate(texture_paths[:main_texture_count]):
                frame_num = i + 1  # 从1开始编号
                file_ext = os.path.splitext(texture_path)[1]  # 获取文件扩展名
                new_name = f"{frame_num}{file_ext}"
                rename_mapping[texture_path] = new_name
                self.logger.debug(f"主贴图重命名: {os.path.basename(texture_path)} -> {new_name}")

            # 按帧顺序重命名辅助贴图文件 - 支持多张辅助贴图
            if all_aux_texture_ids:
                aux_texture_paths = texture_paths[main_texture_count:]

                # 构建辅助贴图的帧号映射
                aux_texture_frame_mapping = self._build_aux_texture_mapping(params)

                # 为每个辅助贴图文件确定帧号和辅助序号
                for i, texture_path in enumerate(aux_texture_paths):
                    original_name = os.path.basename(texture_path)

                    # 尝试从原文件名中提取帧号和辅助序号
                    frame_num = None
                    aux_num = 1  # 默认为第1个辅助贴图

                    # 解析文件名模式：如 "6_1.png", "7_2.png" 等
                    name_without_ext = os.path.splitext(original_name)[0]
                    if '_' in name_without_ext:
                        parts = name_without_ext.split('_')
                        if len(parts) >= 2:
                            try:
                                frame_num = int(parts[0])
                                aux_num = int(parts[1])
                            except ValueError:
                                pass

                    # 如果无法从文件名提取帧号，使用映射表
                    if frame_num is None and i < len(aux_texture_frame_mapping):
                        frame_num, aux_num = aux_texture_frame_mapping[i]

                    # 最后的备用方案
                    if frame_num is None:
                        frame_num = i + 1
                        aux_num = 1

                    file_ext = os.path.splitext(texture_path)[1]  # 获取文件扩展名
                    new_name = f"{frame_num}_{aux_num}{file_ext}"  # 格式：帧号_辅助序号.扩展名
                    rename_mapping[texture_path] = new_name
                    self.logger.debug(f"辅助贴图重命名: {original_name} -> {new_name} (第{frame_num}帧第{aux_num}个辅助贴图)")

            self.logger.info(f"生成重命名映射: {len(rename_mapping)} 个文件")

        except Exception as e:
            self.logger.error(f"生成重命名映射失败: {e}")
            # 如果出错，返回空映射，使用原文件名
            return {}

        return rename_mapping

    def _build_aux_texture_mapping(self, params):
        """构建辅助贴图的帧号映射表 - 基于配置中的实际帧索引"""
        mapping = []

        try:
            amount = params.get('amount', 0)

            # 遍历每一帧，检查是否有辅助贴图
            for frame_index in range(amount):
                frame_num = frame_index + 1  # 帧号从1开始

                # 检查每种辅助贴图类型
                for aux_type in range(1, 5):  # _1, _2, _3, _4
                    aux_key = f'texture_ids_{aux_type}'
                    aux_list = params.get(aux_key, [])

                    # 如果这一帧有这种类型的辅助贴图
                    if frame_index < len(aux_list) and aux_list[frame_index] > 0:
                        mapping.append((frame_num, aux_type))

            self.logger.debug(f"构建辅助贴图映射: {len(mapping)} 个辅助贴图")
            for frame_num, aux_type in mapping:
                self.logger.debug(f"  第{frame_num}帧第{aux_type}个辅助贴图")

        except Exception as e:
            self.logger.error(f"构建辅助贴图映射失败: {e}")

        return mapping

    def _save_effect_config(self, output_dir, effect_title, effect_config):
        """保存光效配置到ini文件"""
        try:
            config_file = os.path.join(output_dir, f"{effect_title}.ini")

            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(f"[{effect_title}]\n")

                # 写入所有配置项
                for key, value in effect_config.items():
                    f.write(f"{key}={value}\n")

            self.logger.info(f"保存光效配置: {config_file}")

        except Exception as e:
            self.logger.error(f"保存光效配置失败: {e}")
    
    def extract_effect(self, client_path, output_path, effect_title, progress_callback=None):
        """主提取流程"""
        try:
            self.logger.info(f"开始提取光效: {effect_title}")
            self.logger.info(f"客户端路径: {client_path}")
            self.logger.info(f"输出路径: {output_path}")
            
            if progress_callback:
                progress_callback("正在查找光效配置...")
            
            # 1. 查找光效配置
            effect_config, found_file = self.find_effect_config(client_path, effect_title)
            if not effect_config:
                raise Exception(f"未找到光效配置: {effect_title}")
            
            if progress_callback:
                progress_callback("正在解析光效参数...")
            
            # 2. 解析光效参数
            params = self.parse_effect_params(effect_config)
            if not params:
                raise Exception("解析光效参数失败")
            
            if progress_callback:
                progress_callback("正在获取资源路径...")
            
            # 3. 获取资源路径
            model_paths, texture_paths = self.get_resource_paths(
                client_path, params['effect_ids'], params['texture_ids'],
                params['texture_ids_1'], params['texture_ids_2'],
                params['texture_ids_3'], params['texture_ids_4']
            )
            
            if progress_callback:
                progress_callback("正在复制资源文件...")
            
            # 4. 复制资源文件
            copied_files = self.copy_resources(
                client_path, output_path, effect_title, model_paths, texture_paths, effect_config
            )
            
            if progress_callback:
                progress_callback("提取完成!")
            
            self.logger.info(f"光效提取完成! 共复制 {len(copied_files)} 个文件")
            return True, copied_files
            
        except Exception as e:
            error_msg = f"光效提取失败: {e}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(error_msg)
            return False, str(e)

    def extract_effects_from_file(self, client_path, output_path, title_file_path, progress_callback=None):
        """从txt文件批量提取光效 - 优化版本"""
        try:
            self.logger.info(f"开始批量提取光效，配置文件: {title_file_path}")

            # 读取title列表
            if not os.path.exists(title_file_path):
                raise Exception(f"配置文件不存在: {title_file_path}")

            with open(title_file_path, 'r', encoding='utf-8') as f:
                titles = [line.strip() for line in f.readlines()
                         if line.strip() and not line.strip().startswith('#')]

            if not titles:
                raise Exception("配置文件为空或没有有效的title")

            self.logger.info(f"找到 {len(titles)} 个光效title需要提取")

            # 智能预加载 - 构建索引和缓存
            if progress_callback:
                progress_callback("正在构建光效索引和资源映射...")
            self._smart_preload(client_path)

            results = {
                'success': [],
                'failed': [],
                'total': len(titles)
            }

            for i, title in enumerate(titles, 1):
                try:
                    if progress_callback:
                        progress_callback(f"正在处理 {i}/{len(titles)}: {title}")

                    self.logger.info(f"[{i}/{len(titles)}] 开始提取光效: {title}")

                    success, result = self.extract_effect(client_path, output_path, title)

                    if success:
                        results['success'].append({
                            'title': title,
                            'files': result
                        })
                        self.logger.info(f"✅ {title} 提取成功，复制了 {len(result)} 个文件")
                    else:
                        results['failed'].append({
                            'title': title,
                            'error': result
                        })
                        self.logger.error(f"❌ {title} 提取失败: {result}")

                except Exception as e:
                    error_msg = f"处理 {title} 时出错: {e}"
                    results['failed'].append({
                        'title': title,
                        'error': error_msg
                    })
                    self.logger.error(error_msg)

            # 生成批量处理报告
            self._generate_batch_report(output_path, results)

            if progress_callback:
                progress_callback(f"批量提取完成! 成功: {len(results['success'])}, 失败: {len(results['failed'])}")

            self.logger.info(f"批量提取完成! 成功: {len(results['success'])}, 失败: {len(results['failed'])}")
            return True, results

        except Exception as e:
            error_msg = f"批量提取失败: {e}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(error_msg)
            return False, str(e)

    def _smart_preload(self, client_path):
        """智能预加载 - 并行构建索引和缓存"""
        from concurrent.futures import ThreadPoolExecutor

        start_time = time.time()

        # 并行执行预加载任务
        with ThreadPoolExecutor(max_workers=3) as executor:
            # 任务1: 构建光效索引
            index_future = executor.submit(self._build_effect_index, client_path)

            # 任务2: 预加载资源映射
            mapping_future = executor.submit(self._preload_resource_mappings, client_path)

            # 等待所有任务完成
            index_future.result()
            mapping_future.result()

        preload_time = time.time() - start_time
        self.logger.info(f"智能预加载完成，耗时 {preload_time:.3f}秒")

    def _preload_resource_mappings(self, client_path):
        """预加载资源映射文件到缓存"""
        ini_dir = os.path.join(client_path, 'ini')

        # 预加载模型映射
        effect_obj_ini = os.path.join(ini_dir, '3deffectobj.ini')
        if os.path.exists(effect_obj_ini):
            self._parse_resource_mapping_cached(effect_obj_ini, [], "模型")

        # 预加载贴图映射
        texture_ini = os.path.join(ini_dir, '3dtexture.ini')
        if os.path.exists(texture_ini):
            self._parse_resource_mapping_cached(texture_ini, [], "贴图")

    def _generate_batch_report(self, output_path, results):
        """生成批量处理报告"""
        try:
            report_file = os.path.join(output_path, '批量提取报告.txt')

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("光效资源批量提取报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"总计: {results['total']} 个光效\n")
                f.write(f"成功: {len(results['success'])} 个\n")
                f.write(f"失败: {len(results['failed'])} 个\n\n")

                if results['success']:
                    f.write("成功提取的光效:\n")
                    f.write("-" * 30 + "\n")
                    for item in results['success']:
                        f.write(f"✅ {item['title']} (复制了 {len(item['files'])} 个文件)\n")
                    f.write("\n")

                if results['failed']:
                    f.write("提取失败的光效:\n")
                    f.write("-" * 30 + "\n")
                    for item in results['failed']:
                        f.write(f"❌ {item['title']}: {item['error']}\n")
                    f.write("\n")

            self.logger.info(f"生成批量处理报告: {report_file}")

        except Exception as e:
            self.logger.error(f"生成批量处理报告失败: {e}")


class EffectExtractorGUI:
    def __init__(self):
        self.extractor = EffectExtractor()
        self.setup_gui()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("光效资源提取工具")
        self.root.geometry("800x600")
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 客户端路径
        ttk.Label(main_frame, text="客户端根目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.client_path_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.client_path_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_client_path).grid(row=0, column=2, padx=5)
        
        # 输出路径
        ttk.Label(main_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.output_path_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.output_path_var, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_output_path).grid(row=1, column=2, padx=5)
        
        # 提取模式选择
        ttk.Label(main_frame, text="提取模式:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.extract_mode_var = tk.StringVar(value="single")
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Radiobutton(mode_frame, text="单个光效", variable=self.extract_mode_var, value="single", command=self.on_mode_change).pack(side=tk.LEFT)
        ttk.Radiobutton(mode_frame, text="批量提取", variable=self.extract_mode_var, value="batch", command=self.on_mode_change).pack(side=tk.LEFT, padx=(20, 0))

        # 单个光效输入
        self.single_frame = ttk.Frame(main_frame)
        self.single_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(self.single_frame, text="光效Title名称:").grid(row=0, column=0, sticky=tk.W)
        self.effect_title_var = tk.StringVar()
        ttk.Entry(self.single_frame, textvariable=self.effect_title_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)

        # 批量提取输入
        self.batch_frame = ttk.Frame(main_frame)
        self.batch_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(self.batch_frame, text="Title列表文件:").grid(row=0, column=0, sticky=tk.W)
        self.title_file_var = tk.StringVar()
        ttk.Entry(self.batch_frame, textvariable=self.title_file_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(self.batch_frame, text="浏览", command=self.browse_title_file).grid(row=0, column=2, padx=5)

        # 提取按钮
        ttk.Button(main_frame, text="开始提取", command=self.start_extraction).grid(row=5, column=1, pady=20)

        # 初始化界面状态
        self.on_mode_change()
        
        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=6, column=0, columnspan=3, pady=5)

        # 日志输出区域
        ttk.Label(main_frame, text="日志输出:").grid(row=7, column=0, sticky=tk.W, pady=(20, 5))
        self.log_text = scrolledtext.ScrolledText(main_frame, height=20, width=80)
        self.log_text.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)

        # 配置日志文本区域的网格权重
        main_frame.rowconfigure(8, weight=1)
        
        # 重定向日志到GUI
        self.setup_logging_redirect()
    
    def setup_logging_redirect(self):
        """设置日志重定向到GUI"""
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
            
            def emit(self, record):
                msg = self.format(record)
                self.text_widget.insert(tk.END, msg + '\n')
                self.text_widget.see(tk.END)
                self.text_widget.update()
        
        # 添加GUI日志处理器
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        self.extractor.logger.addHandler(gui_handler)
    
    def browse_client_path(self):
        """浏览客户端路径"""
        path = filedialog.askdirectory(title="选择客户端根目录")
        if path:
            self.client_path_var.set(path)
    
    def browse_output_path(self):
        """浏览输出路径"""
        path = filedialog.askdirectory(title="选择输出目录")
        if path:
            self.output_path_var.set(path)

    def browse_title_file(self):
        """浏览title列表文件"""
        path = filedialog.askopenfilename(
            title="选择Title列表文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if path:
            self.title_file_var.set(path)

    def on_mode_change(self):
        """切换提取模式"""
        mode = self.extract_mode_var.get()
        if mode == "single":
            self.single_frame.grid()
            self.batch_frame.grid_remove()
        else:
            self.single_frame.grid_remove()
            self.batch_frame.grid()
    
    def update_progress(self, message):
        """更新进度显示"""
        self.progress_var.set(message)
        self.root.update()
    
    def start_extraction(self):
        """开始提取"""
        client_path = self.client_path_var.get().strip()
        output_path = self.output_path_var.get().strip()
        mode = self.extract_mode_var.get()

        # 基础验证
        if not client_path or not output_path:
            messagebox.showerror("错误", "请填写客户端路径和输出路径")
            return

        if not os.path.exists(client_path):
            messagebox.showerror("错误", "客户端路径不存在")
            return

        if not os.path.exists(output_path):
            messagebox.showerror("错误", "输出路径不存在")
            return

        # 根据模式验证特定输入
        if mode == "single":
            effect_title = self.effect_title_var.get().strip()
            if not effect_title:
                messagebox.showerror("错误", "请输入光效Title名称")
                return

            # 单个提取
            def single_extraction_thread():
                success, result = self.extractor.extract_effect(
                    client_path, output_path, effect_title, self.update_progress
                )

                if success:
                    messagebox.showinfo("成功", f"光效提取完成!\n共复制 {len(result)} 个文件")
                else:
                    messagebox.showerror("错误", f"提取失败: {result}")

            threading.Thread(target=single_extraction_thread, daemon=True).start()

        else:  # batch mode
            title_file = self.title_file_var.get().strip()
            if not title_file:
                messagebox.showerror("错误", "请选择Title列表文件")
                return

            if not os.path.exists(title_file):
                messagebox.showerror("错误", "Title列表文件不存在")
                return

            # 批量提取
            def batch_extraction_thread():
                success, result = self.extractor.extract_effects_from_file(
                    client_path, output_path, title_file, self.update_progress
                )

                if success:
                    success_count = len(result['success'])
                    failed_count = len(result['failed'])
                    total_count = result['total']

                    message = f"批量提取完成!\n"
                    message += f"总计: {total_count} 个光效\n"
                    message += f"成功: {success_count} 个\n"
                    message += f"失败: {failed_count} 个\n\n"
                    message += f"详细报告已保存到输出目录"

                    messagebox.showinfo("批量提取完成", message)
                else:
                    messagebox.showerror("错误", f"批量提取失败: {result}")

            threading.Thread(target=batch_extraction_thread, daemon=True).start()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = EffectExtractorGUI()
    app.run()
