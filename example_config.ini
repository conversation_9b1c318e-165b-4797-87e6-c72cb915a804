# 示例光效配置文件格式
# 这个文件展示了3deffect2.ini和3deffect3.ini的典型格式

[smgx1_pkyh24_zsyj]
Amount=7
EffectId0=111070
TextureId0=116978
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
EffectId1=111071
TextureId1=116979
ASB1=5
ADB1=2
ZTest1=1
Lev1=1
EffectId2=111072
TextureId2=116980
ASB2=5
ADB2=2
ZTest2=1
Lev2=1
EffectId3=111073
TextureId3=116981
ASB3=5
ADB3=2
ZTest3=1
Lev3=1
EffectId4=111074
TextureId4=116982
ASB4=5
ADB4=2
ZTest4=1
Lev4=1
EffectId5=111075
TextureId5=116983
ASB5=5
ADB5=2
ZTest5=1
Lev5=1
EffectId6=111076
TextureId6=116984
ASB6=5
ADB6=2
ZTest6=1
Lev6=1
Delay=0
LoopTime=99999999
FrameInterval=33
LoopInterval=0
OffsetX=0
OffsetY=0
OffsetZ=0
ManualUVStep=0

[another_effect_example]
Amount=3
EffectId0=222001
TextureId0=223001
ASB0=3
ADB0=1
ZTest0=0
Lev0=2
EffectId1=222002
TextureId1=223002
ASB1=3
ADB1=1
ZTest1=0
Lev1=2
EffectId2=222003
TextureId2=223003
ASB2=3
ADB2=1
ZTest2=0
Lev2=2
Delay=100
LoopTime=5000
FrameInterval=50
LoopInterval=1000
OffsetX=10
OffsetY=20
OffsetZ=30
ManualUVStep=1

# 3deffectobj.ini 示例格式
# [DEFAULT]
# 111070=c3/effect/interface/smgx1_pkyh24_zsyj/1.c3
# 111071=c3/effect/interface/smgx1_pkyh24_zsyj/2.c3
# 111072=c3/effect/interface/smgx1_pkyh24_zsyj/3.c3
# 222001=c3/effect/interface/another_effect/model1.c3

# 3dtexture.ini 示例格式
# [DEFAULT]
# 116978=texture/effect/smgx1_pkyh24_zsyj_01.tga
# 116979=texture/effect/smgx1_pkyh24_zsyj_02.tga
# 116980=texture/effect/smgx1_pkyh24_zsyj_03.tga
# 223001=texture/effect/another_effect_01.tga

# 注意：提取后的模型文件和贴图文件会存放在同一个目录下
# 输出结构：输出目录/客户端更新/c3/effect/interface/[光效名称]/
#   ├── 1.c3
#   ├── 2.c3
#   ├── texture1.tga
#   └── texture2.tga
