# 光效资源提取工具 - 目录结构演示

## 📁 修改后的输出结构

根据您的反馈，光效的模型文件和贴图文件现在存放在同一个目录下，这样更符合游戏资源的组织方式。

### 🎯 新的输出结构

```
输出目录/
└── 客户端更新/
    └── c3/
        └── effect/
            └── interface/
                └── [光效title名称]/
                    ├── 1.c3              # 模型文件1
                    ├── 2.c3              # 模型文件2
                    ├── 3.c3              # 模型文件3
                    ├── texture1.tga      # 贴图文件1
                    ├── texture2.tga      # 贴图文件2
                    └── texture3.tga      # 贴图文件3
```

### 🔄 与之前的区别

**之前的结构（已修改）：**
```
└── [光效title名称]/
    ├── 1.c3
    ├── 2.c3
    └── texture/          # 贴图单独放在子目录
        ├── texture1.tga
        └── texture2.tga
```

**现在的结构（当前）：**
```
└── [光效title名称]/
    ├── 1.c3             # 模型文件
    ├── 2.c3             # 模型文件
    ├── texture1.tga     # 贴图文件，与模型文件在同一目录
    └── texture2.tga     # 贴图文件，与模型文件在同一目录
```

### ✅ 优势说明

1. **统一管理**：模型和贴图在同一目录，便于整体管理
2. **引擎友好**：游戏引擎可以更容易地加载相关资源
3. **路径简化**：减少了子目录层级，路径更简洁
4. **打包方便**：整个光效的所有文件都在一个目录下

### 🛠️ 实现细节

在 `copy_resources` 方法中的关键修改：

```python
# 复制贴图文件 - 与模型文件存放在同一目录
for texture_path in texture_paths:
    src_path = os.path.join(client_path, texture_path)
    if os.path.exists(src_path):
        # 贴图文件与模型文件存放在同一目录下
        filename = os.path.basename(texture_path)
        dst_path = os.path.join(base_output_dir, filename)  # 直接放在基础目录
        
        # 复制文件
        shutil.copy2(src_path, dst_path)
        copied_files.append(dst_path)
        self.logger.info(f"复制贴图文件: {src_path} -> {dst_path}")
```

### 📋 实际示例

假设提取光效 `smgx1_pkyh24_zsyj`，输出结构如下：

```
输出目录/
└── 客户端更新/
    └── c3/
        └── effect/
            └── interface/
                └── smgx1_pkyh24_zsyj/
                    ├── 1.c3                    # EffectId0 对应的模型
                    ├── 2.c3                    # EffectId1 对应的模型
                    ├── 3.c3                    # EffectId2 对应的模型
                    ├── smgx1_pkyh24_zsyj_01.tga # TextureId0 对应的贴图
                    ├── smgx1_pkyh24_zsyj_02.tga # TextureId1 对应的贴图
                    └── smgx1_pkyh24_zsyj_03.tga # TextureId2 对应的贴图
```

### 🧪 测试验证

测试结果显示新的目录结构工作正常：

```
✅ 提取成功!
复制的文件: [
    'C:\...\output\客户端更新\c3\effect\interface\test_effect\1.c3',
    'C:\...\output\客户端更新\c3\effect\interface\test_effect\2.c3', 
    'C:\...\output\客户端更新\c3\effect\interface\test_effect\test_effect_01.tga',
    'C:\...\output\客户端更新\c3\effect\interface\test_effect\test_effect_02.tga'
]
```

可以看到，所有文件（模型和贴图）都在同一个目录 `test_effect` 下。

### 📝 使用说明

这个修改不会影响工具的使用方式，只是改变了输出文件的组织结构：

1. **GUI界面**：无需任何改变，直接使用
2. **编程接口**：API保持不变
3. **配置文件**：INI文件格式和解析逻辑不变
4. **日志输出**：会显示正确的目标路径

### 🔧 自定义调整

如果需要进一步调整目录结构，可以修改 `copy_resources` 方法中的路径生成逻辑：

```python
# 示例：如果需要按文件类型分组
if filename.endswith('.c3'):
    dst_path = os.path.join(base_output_dir, 'models', filename)
elif filename.endswith('.tga'):
    dst_path = os.path.join(base_output_dir, 'textures', filename)
else:
    dst_path = os.path.join(base_output_dir, filename)
```

但根据您的需求，当前的"统一目录"结构是最合适的。
