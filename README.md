# 光效资源提取工具

这是一个用于从游戏客户端中提取光效资源文件的Python工具，具有图形化用户界面。

## 功能特性

- 🎯 根据光效title名称自动提取相关资源
- 📁 自动组织输出目录结构
- 🖥️ 友好的图形化界面
- 📝 详细的日志输出
- ⚡ 多线程处理，界面不卡顿
- 🔍 智能解析INI配置文件
- 🛠️ **支持复杂光效参数**：TextureId_1、MixOpt等高级参数
- 🔧 **强化INI解析**：处理重复键名和无section格式
- 🖼️ **多格式支持**：支持.png、.tga等多种贴图格式
- 📄 **配置保存**：自动保存光效完整配置到输出目录
- 📋 **批量处理**：支持txt文件批量提取多个光效

## 系统要求

- Python 3.6+
- tkinter (通常随Python一起安装)
- 标准库模块：configparser, pathlib, logging, threading

## 安装和使用

### 1. 直接运行
```bash
python effect_extractor.py
```

### 2. 使用步骤

#### 单个光效提取
1. **选择客户端根目录**：点击"浏览"按钮选择游戏客户端的根目录
2. **选择输出目录**：点击"浏览"按钮选择资源输出的目标目录
3. **选择提取模式**：选择"单个光效"模式
4. **输入光效名称**：在文本框中输入要提取的光效title名称（如：smgx1_pkyh24_zsyj）
5. **开始提取**：点击"开始提取"按钮启动提取过程
6. **查看结果**：在日志输出区域查看详细的提取过程和结果

#### 批量光效提取
1. **准备title列表文件**：创建一个txt文件，每行写一个光效名称
2. **选择客户端根目录**：点击"浏览"按钮选择游戏客户端的根目录
3. **选择输出目录**：点击"浏览"按钮选择资源输出的目标目录
4. **选择提取模式**：选择"批量提取"模式
5. **选择title列表文件**：点击"浏览"按钮选择包含光效名称的txt文件
6. **开始提取**：点击"开始提取"按钮启动批量提取过程
7. **查看结果**：查看批量提取报告和日志输出

## 工作原理

### 1. 配置文件解析
工具会在客户端的`ini`目录下查找以下文件：
- `3deffect2.ini` - 光效配置文件2
- `3deffect3.ini` - 光效配置文件3
- `3deffectobj.ini` - 光效模型路径映射
- `3dtexture.ini` - 贴图资源路径映射

### 2. 参数解析
从光效配置section中解析以下参数：
```ini
[smgx1_pkyh24_zsyj]
Amount=7
EffectId0=111070
TextureId0=116978
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
Delay=0
LoopTime=99999999
FrameInterval=33
LoopInterval=0
OffsetX=0
OffsetY=0
OffsetZ=0
ManualUVStep=0
```

### 3. 资源映射
- **模型资源**：通过EffectId在`3deffectobj.ini`中查找对应的.c3文件路径
- **贴图资源**：通过TextureId在`3dtexture.ini`中查找对应的贴图文件路径

### 4. 输出结构
提取的资源会按以下简化结构组织：
```
输出目录/
└── 客户端更新/
    ├── [光效title1]/
    │   ├── 1.c3                    # 模型文件
    │   ├── 2.c3
    │   ├── 3.c3
    │   ├── 1.png                   # 贴图文件
    │   ├── 2.png
    │   ├── 3.png
    │   └── [光效title1].ini        # 配置文件
    └── [光效title2]/
        ├── 1.c3
        ├── 2.c3
        ├── 1.png
        ├── 2.png
        └── [光效title2].ini
```

**特点**：
- 🎯 **简化层级**：直接在"客户端更新"下创建光效目录，减少嵌套
- 📁 **统一存放**：模型文件(.c3)和贴图文件(.png)都在同一目录下
- 📄 **配置保存**：每个光效目录包含完整的配置文件

### 5. 批量处理文件格式

创建一个UTF-8编码的txt文件，格式如下：
```
# 光效Title列表
# 以#开头的行是注释，会被忽略
# 空行也会被自动跳过

smgx1_pkyh24_zsyj
smgx1_pkyh24_zsgj
red0
green0
combo0

# 可以添加更多光效名称
# 每行一个光效title
```

### 6. 输出内容

每个光效提取后会包含：
- **资源文件**：模型文件(.c3)和贴图文件(.png)
- **配置文件**：`[光效名称].ini` 包含完整的光效配置参数
- **批量报告**：`批量提取报告.txt` (仅批量模式)

## 错误处理

工具包含完善的错误处理机制：
- ✅ 文件不存在检查
- ✅ INI文件格式验证
- ✅ **重复键名处理** - 自动处理INI文件中的重复键名问题
- ✅ 资源路径映射检查
- ✅ 目录创建权限检查
- ✅ 详细的错误日志输出

### 重复键名处理
如果INI文件中存在重复的键名（如多个`TextureId1`），工具会：
1. 首先尝试标准解析
2. 如果遇到重复键名错误，自动切换到宽松模式
3. 如果仍然失败，使用手动解析模式跳过重复的键
4. 在日志中显示警告信息，但不会中断提取过程

## 日志功能

- 实时显示提取进度
- 详细记录每个步骤的执行情况
- 清晰标识成功和失败的操作
- 支持滚动查看历史日志

## 注意事项

1. **编码格式**：INI文件默认使用GBK编码解析，如遇到编码问题可能需要调整
2. **路径分隔符**：工具会自动处理Windows和Unix风格的路径分隔符
3. **权限要求**：确保对输出目录有写入权限
4. **文件覆盖**：如果目标文件已存在，工具会自动覆盖

## 扩展功能

工具设计为模块化结构，可以轻松扩展：
- 支持批量提取多个光效
- 添加更多资源类型支持
- 自定义输出目录结构
- 集成到其他工具链中

## 故障排除

### 常见问题

1. **找不到光效配置**
   - 检查光效名称是否正确
   - 确认客户端目录结构是否完整

2. **资源文件不存在**
   - 检查客户端文件是否完整
   - 确认INI文件中的路径映射是否正确

3. **权限错误**
   - 确保对输出目录有写入权限
   - 尝试以管理员身份运行

## 技术支持

如果遇到问题，请检查日志输出中的详细错误信息，这将有助于快速定位和解决问题。
