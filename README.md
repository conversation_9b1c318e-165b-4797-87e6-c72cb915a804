# 光效资源提取工具

高性能的游戏光效资源提取工具，支持批量处理、智能重命名和图形界面操作。

## ✨ 核心功能

- 🎯 **精确提取**：根据光效名称精确提取对应的模型和贴图文件
- 📋 **批量处理**：支持从txt文件批量提取多个光效，性能优化
- 🖼️ **图形界面**：提供友好的GUI界面，操作简单直观
- 📁 **简化结构**：模型和贴图统一存放，目录结构简洁
- 🔄 **智能重命名**：按帧顺序重命名文件（1.c3, 2.c3, 1.png, 2.png等）
- 💾 **配置保存**：自动保存光效完整配置到ini文件
- 🔧 **多张辅助贴图**：支持TextureId_1到TextureId_4的多张辅助贴图
- ⚡ **高性能优化**：缓存机制、并行处理、智能索引
- 🛠️ **强化解析**：处理重复键名和复杂ini格式
- 🖼️ **多格式支持**：支持.png、.tga、.c3等多种文件格式

## 📋 系统要求

- Python 3.7+
- tkinter (通常随Python安装)
- 游戏客户端文件（包含ini目录）

## 🚀 快速开始

### 运行工具
双击 `run_extractor.bat` 或命令行运行：
```bash
python effect_extractor.py
```

### 使用步骤
1. **选择客户端路径**：选择游戏客户端目录
2. **选择输出路径**：选择资源输出目录
3. **输入光效名称**：输入要提取的光效名称
4. **开始提取**：点击按钮开始提取

### 批量提取
1. 准备txt文件，每行一个光效名称
2. 点击"批量提取"选择文件
3. 自动处理所有光效

## 📁 输出结构

```
输出目录/
└── 客户端更新/
    └── [光效名称]/
        ├── 1.c3          # 第1帧模型
        ├── 2.c3          # 第2帧模型
        ├── 3.c3          # 第3帧模型
        ├── 1.png         # 第1帧主贴图
        ├── 2.png         # 第2帧主贴图
        ├── 3.png         # 第3帧主贴图
        ├── 2_1.png       # 第2帧第1张辅助贴图
        ├── 3_1.png       # 第3帧第1张辅助贴图
        ├── 3_2.png       # 第3帧第2张辅助贴图
        └── [光效名称].ini # 配置文件
```

## 🎯 重命名规则

- **模型文件**：1.c3, 2.c3, 3.c3, ...
- **主贴图**：1.png, 2.png, 3.png, ...
- **辅助贴图**：帧号_序号.png (如: 2_1.png, 3_2.png)

## 🔧 支持的配置

```ini
[光效名称]
Amount=3
EffectId0=111070
TextureId0=116978
TextureId0_1=117094    # 第1张辅助贴图
TextureId0_2=117095    # 第2张辅助贴图
EffectId1=111071
TextureId1=116979
EffectId2=111072
TextureId2=116980
TextureId2_1=117096    # 第3帧第1张辅助贴图
```

## ⚡ 性能特点

- **光效索引**：21,908个光效索引仅需0.2秒构建
- **缓存加速**：1000+倍的查找加速效果
- **内存高效**：20万+映射记录仅占21MB内存
- **并行处理**：多线程文件复制，充分利用多核CPU
- **智能预加载**：批量处理时避免重复初始化

## 📝 示例文件

- `title_list_example.txt` - 批量提取示例
- `example_config.ini` - 光效配置示例
- `使用示例.md` - 详细使用说明

## ⚠️ 注意事项

1. 确保客户端目录包含 `ini` 子目录
2. 需要以下映射文件：
   - `3deffect2.ini` 或 `3deffect3.ini` (光效配置)
   - `3deffectobj.ini` (模型映射)
   - `3dtexture.ini` (贴图映射)
3. 光效名称区分大小写

## 🔍 故障排除

**未找到光效配置**：检查光效名称拼写
**文件不存在**：确认客户端路径正确
**批量提取失败**：查看生成的处理报告

工具会输出详细日志帮助诊断问题。

## 📄 许可证

仅供学习研究使用。
