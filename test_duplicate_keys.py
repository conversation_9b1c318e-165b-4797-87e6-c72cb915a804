#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复键名INI文件解析
"""

import os
import tempfile
import shutil
from effect_extractor import EffectExtractor


def create_problematic_ini():
    """创建包含重复键名的测试INI文件"""
    content = """[test_section]
Amount=3
EffectId0=111
TextureId0=222
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
EffectId1=333
TextureId1=444
ASB1=3
ADB1=1
ZTest1=0
Lev1=2
EffectId2=555
TextureId2=666
ASB2=4
ADB2=3
ZTest2=1
Lev2=1
# 这里故意添加重复的键名
TextureId1=777
ASB1=6
ZTest1=1
Delay=100
LoopTime=5000
FrameInterval=50
LoopInterval=1000
OffsetX=10
OffsetY=20
OffsetZ=30
ManualUVStep=1

[electric_1_999]
Amount=10
EffectId0=100001
TextureId0=200001
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
EffectId1=100002
TextureId1=200002
ASB1=5
ADB1=2
ZTest1=1
Lev1=1
EffectId2=100003
TextureId2=200003
ASB2=5
ADB2=2
ZTest2=1
Lev2=1
EffectId3=100004
TextureId3=200004
ASB3=5
ADB3=2
ZTest3=1
Lev3=1
EffectId4=100005
TextureId4=200005
ASB4=5
ADB4=2
ZTest4=1
Lev4=1
EffectId5=100006
TextureId5=200006
ASB5=5
ADB5=2
ZTest5=1
Lev5=1
EffectId6=100007
TextureId6=200007
ASB6=5
ADB6=2
ZTest6=1
Lev6=1
EffectId7=100008
TextureId7=200008
ASB7=5
ADB7=2
ZTest7=1
Lev7=1
EffectId8=100009
TextureId8=200009
ASB8=5
ADB8=2
ZTest8=1
Lev8=1
EffectId9=100010
TextureId9=200010
ASB9=5
ADB9=2
ZTest9=1
Lev9=1
# 故意重复TextureId9
TextureId9=200011
Delay=0
LoopTime=99999999
FrameInterval=33
LoopInterval=0
OffsetX=0
OffsetY=0
OffsetZ=0
ManualUVStep=0

[smgx0_marry_success]
Amount=4
EffectId0=111111
TextureId0=222222
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
EffectId1=111112
TextureId1=222223
ASB1=5
ADB1=2
ZTest1=1
Lev1=1
EffectId2=111113
TextureId2=222224
ASB2=5
ADB2=2
ZTest2=1
Lev2=1
EffectId3=111114
TextureId3=222225
ASB3=5
ADB3=2
ZTest3=1
Lev3=1
# 故意重复ASB3
ASB3=6
Delay=200
LoopTime=8000
FrameInterval=40
LoopInterval=500
OffsetX=5
OffsetY=10
OffsetZ=15
ManualUVStep=0
"""
    return content


def test_duplicate_keys_parsing():
    """测试重复键名解析"""
    print("=" * 60)
    print("测试重复键名INI文件解析")
    print("=" * 60)
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="duplicate_test_")
    ini_file = os.path.join(test_dir, "test_duplicate.ini")
    
    try:
        # 创建包含重复键名的INI文件
        content = create_problematic_ini()
        with open(ini_file, 'w', encoding='gbk') as f:
            f.write(content)
        
        print(f"创建测试文件: {ini_file}")
        
        # 创建提取器实例
        extractor = EffectExtractor()
        
        # 测试解析
        print("\n1. 测试解析包含重复键名的INI文件...")
        config = extractor.parse_ini_file(ini_file)
        
        if config:
            print("✅ 解析成功!")
            
            # 检查sections
            sections = config.sections()
            print(f"   找到 {len(sections)} 个sections: {sections}")
            
            # 测试每个section
            for section_name in sections:
                print(f"\n2. 测试section: {section_name}")
                section = config[section_name]
                
                try:
                    # 尝试解析参数
                    params = extractor.parse_effect_params(section)
                    
                    if params:
                        print(f"   ✅ 参数解析成功")
                        print(f"   Amount: {params['amount']}")
                        print(f"   EffectIds: {params['effect_ids']}")
                        print(f"   TextureIds: {params['texture_ids']}")
                        print(f"   Delay: {params['delay']}")
                        
                        # 验证数据完整性
                        if len(params['effect_ids']) == params['amount']:
                            print(f"   ✅ 数据完整性验证通过")
                        else:
                            print(f"   ⚠️ 数据完整性问题: 期望{params['amount']}个，实际{len(params['effect_ids'])}个")
                    else:
                        print(f"   ❌ 参数解析失败")
                        
                except Exception as e:
                    print(f"   ❌ 解析section {section_name} 时出错: {e}")
        else:
            print("❌ 解析失败")
        
        print(f"\n3. 测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    finally:
        # 清理测试文件
        shutil.rmtree(test_dir)
        print(f"已清理测试目录: {test_dir}")


def test_real_world_scenario():
    """测试真实世界场景"""
    print("\n" + "=" * 60)
    print("真实场景测试指南")
    print("=" * 60)
    
    print("如果您遇到了类似的INI文件解析错误，可以尝试以下步骤:")
    print()
    print("1. 使用修改后的工具重新尝试提取")
    print("   - 工具现在可以处理重复键名的情况")
    print("   - 会自动跳过重复的键，使用第一次出现的值")
    print()
    print("2. 检查日志输出")
    print("   - 查看是否有'跳过重复键'的警告信息")
    print("   - 确认手动解析是否成功")
    print()
    print("3. 如果仍然失败，可以手动清理INI文件")
    print("   - 找到重复的键名并删除多余的行")
    print("   - 或者联系开发者获取帮助")
    print()
    print("4. 验证提取结果")
    print("   - 检查输出目录中的文件是否完整")
    print("   - 确认模型和贴图文件都已正确复制")


if __name__ == "__main__":
    test_duplicate_keys_parsing()
    test_real_world_scenario()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
