# 光效资源提取工具依赖
# 本工具主要使用Python标准库，无需额外安装依赖

# 如果需要更高级的功能，可以考虑以下可选依赖：

# 用于更好的配置文件处理
# configparser  # Python 3.2+ 内置

# 用于更好的路径处理
# pathlib  # Python 3.4+ 内置

# 用于GUI界面
# tkinter  # 通常随Python一起安装

# 用于日志记录
# logging  # Python标准库

# 用于多线程处理
# threading  # Python标准库

# 用于文件操作
# shutil  # Python标准库
# os  # Python标准库

# 如果在某些系统上tkinter未安装，可以尝试：
# 在Ubuntu/Debian上: sudo apt-get install python3-tk
# 在CentOS/RHEL上: sudo yum install tkinter
# 在Windows上: tkinter通常随Python一起安装
# 在macOS上: tkinter通常随Python一起安装

# 可选：用于更好的错误追踪
# traceback  # Python标准库

# 可选：用于临时文件处理（测试时使用）
# tempfile  # Python标准库
