#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光效资源提取工具测试脚本
用于测试提取工具的各项功能
"""

import os
import tempfile
import shutil
from effect_extractor import EffectExtractor


def create_test_environment():
    """创建测试环境"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="effect_test_")
    client_dir = os.path.join(test_dir, "client")
    output_dir = os.path.join(test_dir, "output")
    
    # 创建客户端目录结构
    ini_dir = os.path.join(client_dir, "ini")
    effect_dir = os.path.join(client_dir, "c3", "effect", "interface", "test_effect")
    texture_dir = os.path.join(client_dir, "texture", "effect")
    
    os.makedirs(ini_dir)
    os.makedirs(effect_dir)
    os.makedirs(texture_dir)
    os.makedirs(output_dir)
    
    # 创建测试INI文件
    create_test_ini_files(ini_dir)
    
    # 创建测试资源文件
    create_test_resource_files(effect_dir, texture_dir)
    
    return test_dir, client_dir, output_dir


def create_test_ini_files(ini_dir):
    """创建测试INI文件"""
    
    # 创建3deffect2.ini
    effect2_content = """[test_effect]
Amount=2
EffectId0=100001
TextureId0=200001
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
EffectId1=100002
TextureId1=200002
ASB1=3
ADB1=1
ZTest1=0
Lev1=2
Delay=0
LoopTime=99999999
FrameInterval=33
LoopInterval=0
OffsetX=0
OffsetY=0
OffsetZ=0
ManualUVStep=0

[another_effect]
Amount=1
EffectId0=100003
TextureId0=200003
ASB0=4
ADB0=3
ZTest0=1
Lev0=1
Delay=100
LoopTime=5000
FrameInterval=50
LoopInterval=1000
OffsetX=10
OffsetY=20
OffsetZ=30
ManualUVStep=1
"""
    
    with open(os.path.join(ini_dir, "3deffect2.ini"), "w", encoding="gbk") as f:
        f.write(effect2_content)
    
    # 创建3deffectobj.ini
    effectobj_content = """[DEFAULT]
100001=c3/effect/interface/test_effect/1.c3
100002=c3/effect/interface/test_effect/2.c3
100003=c3/effect/interface/another_effect/model.c3
"""
    
    with open(os.path.join(ini_dir, "3deffectobj.ini"), "w", encoding="gbk") as f:
        f.write(effectobj_content)
    
    # 创建3dtexture.ini
    texture_content = """[DEFAULT]
200001=texture/effect/test_effect_01.tga
200002=texture/effect/test_effect_02.tga
200003=texture/effect/another_effect_01.tga
"""
    
    with open(os.path.join(ini_dir, "3dtexture.ini"), "w", encoding="gbk") as f:
        f.write(texture_content)


def create_test_resource_files(effect_dir, texture_dir):
    """创建测试资源文件"""

    # 创建模型文件
    with open(os.path.join(effect_dir, "1.c3"), "w") as f:
        f.write("# Test C3 Model File 1\n")

    with open(os.path.join(effect_dir, "2.c3"), "w") as f:
        f.write("# Test C3 Model File 2\n")

    # 创建贴图文件 - 现在也放在effect_dir中，因为模型和贴图存放在一起
    with open(os.path.join(effect_dir, "test_effect_01.tga"), "w") as f:
        f.write("# Test Texture File 1\n")

    with open(os.path.join(effect_dir, "test_effect_02.tga"), "w") as f:
        f.write("# Test Texture File 2\n")

    # 同时在原texture目录也创建文件，以测试路径映射
    with open(os.path.join(texture_dir, "test_effect_01.tga"), "w") as f:
        f.write("# Test Texture File 1 (Original Location)\n")

    with open(os.path.join(texture_dir, "test_effect_02.tga"), "w") as f:
        f.write("# Test Texture File 2 (Original Location)\n")


def test_effect_extraction():
    """测试光效提取功能"""
    print("开始测试光效提取工具...")
    
    # 创建测试环境
    test_dir, client_dir, output_dir = create_test_environment()
    
    try:
        # 创建提取器实例
        extractor = EffectExtractor()
        
        # 测试提取功能
        print(f"测试目录: {test_dir}")
        print(f"客户端目录: {client_dir}")
        print(f"输出目录: {output_dir}")
        
        # 执行提取
        success, result = extractor.extract_effect(
            client_dir, output_dir, "test_effect"
        )
        
        if success:
            print("✅ 提取成功!")
            print(f"复制的文件: {result}")
            
            # 验证输出文件
            expected_output_dir = os.path.join(output_dir, "客户端更新", "c3", "effect", "interface", "test_effect")
            if os.path.exists(expected_output_dir):
                print("✅ 输出目录创建成功")
                
                # 检查文件是否存在
                files = []
                for root, dirs, filenames in os.walk(expected_output_dir):
                    for filename in filenames:
                        files.append(os.path.join(root, filename))
                
                print(f"输出文件列表: {files}")
                
                if len(files) > 0:
                    print("✅ 资源文件复制成功")
                else:
                    print("❌ 未找到复制的资源文件")
            else:
                print("❌ 输出目录未创建")
        else:
            print(f"❌ 提取失败: {result}")
        
        # 测试不存在的光效
        print("\n测试不存在的光效...")
        success, result = extractor.extract_effect(
            client_dir, output_dir, "nonexistent_effect"
        )
        
        if not success:
            print("✅ 正确处理了不存在的光效")
        else:
            print("❌ 应该失败但却成功了")
    
    finally:
        # 清理测试环境
        shutil.rmtree(test_dir)
        print(f"\n测试完成，已清理测试目录: {test_dir}")


def test_ini_parsing():
    """测试INI文件解析功能"""
    print("\n测试INI文件解析...")
    
    extractor = EffectExtractor()
    
    # 创建临时INI文件
    test_dir = tempfile.mkdtemp(prefix="ini_test_")
    ini_file = os.path.join(test_dir, "test.ini")
    
    try:
        # 写入测试内容
        content = """[test_section]
Amount=3
EffectId0=111
TextureId0=222
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
EffectId1=333
TextureId1=444
ASB1=3
ADB1=1
ZTest1=0
Lev1=2
EffectId2=555
TextureId2=666
ASB2=4
ADB2=3
ZTest2=1
Lev2=1
Delay=100
LoopTime=5000
FrameInterval=50
LoopInterval=1000
OffsetX=10
OffsetY=20
OffsetZ=30
ManualUVStep=1
"""
        
        with open(ini_file, "w", encoding="gbk") as f:
            f.write(content)
        
        # 解析INI文件
        config = extractor.parse_ini_file(ini_file)
        
        if config and "test_section" in config.sections():
            print("✅ INI文件解析成功")
            
            # 解析参数
            params = extractor.parse_effect_params(config["test_section"])
            
            if params:
                print("✅ 参数解析成功")
                print(f"Amount: {params['amount']}")
                print(f"EffectIds: {params['effect_ids']}")
                print(f"TextureIds: {params['texture_ids']}")
                print(f"Delay: {params['delay']}")
                
                # 验证数据
                if (params['amount'] == 3 and 
                    len(params['effect_ids']) == 3 and
                    params['effect_ids'] == [111, 333, 555] and
                    params['texture_ids'] == [222, 444, 666]):
                    print("✅ 参数数据验证成功")
                else:
                    print("❌ 参数数据验证失败")
            else:
                print("❌ 参数解析失败")
        else:
            print("❌ INI文件解析失败")
    
    finally:
        shutil.rmtree(test_dir)


if __name__ == "__main__":
    print("=" * 50)
    print("光效资源提取工具测试")
    print("=" * 50)
    
    # 运行测试
    test_ini_parsing()
    test_effect_extraction()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
