#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光效资源提取工具 - 版本信息
"""

__version__ = "1.3.0"
__author__ = "AI Assistant"
__email__ = ""
__description__ = "游戏光效资源提取工具"
__license__ = "MIT"

# 版本历史
VERSION_HISTORY = [
    {
        "version": "1.3.0",
        "date": "2025-08-06",
        "changes": [
            "📁 目录结构简化 - 直接在客户端更新下创建光效目录",
            "🎯 减少嵌套层级 - 去除c3/effect/interface/多层嵌套",
            "📂 统一文件存放 - 模型、贴图、配置文件都在同一目录",
            "🚀 提高访问效率 - 更快的文件访问和操作速度",
            "💾 节省磁盘空间 - 减少目录结构占用空间",
            "🧪 完整测试验证 - 确保新结构正常工作"
        ]
    },
    {
        "version": "1.2.0",
        "date": "2025-08-06",
        "changes": [
            "📄 新增配置保存功能 - 自动保存光效完整配置到ini文件",
            "📋 新增批量处理功能 - 支持txt文件批量提取多个光效",
            "🛠️ 支持复杂光效参数 - TextureId_1、MixOpt等高级参数",
            "🔧 强化INI解析 - 处理重复键名和无section格式",
            "🖼️ 多格式支持 - 支持.png、.tga等多种贴图格式",
            "📊 批量处理报告 - 自动生成详细的处理结果报告",
            "🎯 真实客户端适配 - 基于实际客户端配置优化",
            "⚡ 性能优化 - 改进大文件解析速度"
        ]
    },
    {
        "version": "1.1.0",
        "date": "2025-08-06",
        "changes": [
            "🔧 真实客户端配置支持",
            "🛠️ 处理复杂光效参数",
            "🔍 强化INI文件解析",
            "📁 扩展参数支持",
            "🖼️ 文件格式兼容性"
        ]
    },
    {
        "version": "1.0.0",
        "date": "2025-08-06",
        "changes": [
            "✨ 初始版本发布",
            "🎯 支持根据光效title名称提取资源",
            "📁 自动组织输出目录结构",
            "🖥️ 图形化用户界面",
            "📝 详细的日志输出",
            "⚡ 多线程处理",
            "🔍 智能解析INI配置文件",
            "🧪 完整的测试套件",
            "📚 详细的文档说明"
        ]
    }
]

# 功能特性
FEATURES = [
    "支持解析3deffect2.ini和3deffect3.ini配置文件",
    "自动查找和映射EffectId到模型文件路径",
    "自动查找和映射TextureId到贴图文件路径",
    "支持TextureId_1辅助贴图和MixOpt混合选项",
    "按指定目录结构组织输出文件",
    "自动保存光效完整配置到ini文件",
    "支持txt文件批量提取多个光效",
    "自动生成批量处理报告",
    "友好的图形用户界面",
    "实时进度显示和日志输出",
    "完善的错误处理和提示",
    "处理重复键名和无section格式INI文件",
    "支持.png、.tga等多种贴图格式",
    "编程式API接口",
    "跨平台兼容性"
]

# 系统要求
REQUIREMENTS = {
    "python_version": "3.6+",
    "dependencies": [
        "tkinter (通常随Python安装)",
        "configparser (Python标准库)",
        "pathlib (Python标准库)",
        "logging (Python标准库)",
        "threading (Python标准库)",
        "shutil (Python标准库)",
        "os (Python标准库)"
    ],
    "platforms": [
        "Windows 7/8/10/11",
        "macOS 10.12+",
        "Linux (Ubuntu 16.04+, CentOS 7+)"
    ]
}

# 支持的文件格式
SUPPORTED_FORMATS = {
    "config_files": [
        "3deffect2.ini - 光效配置文件2",
        "3deffect3.ini - 光效配置文件3",
        "3deffectobj.ini - 模型路径映射文件",
        "3dtexture.ini - 贴图路径映射文件"
    ],
    "resource_files": [
        ".c3 - 3D模型文件",
        ".tga - 贴图文件",
        "其他游戏资源文件"
    ]
}

def print_version_info():
    """打印版本信息"""
    print("=" * 60)
    print(f"光效资源提取工具 v{__version__}")
    print("=" * 60)
    print(f"描述: {__description__}")
    print(f"作者: {__author__}")
    print(f"许可: {__license__}")
    print()
    
    print("🚀 功能特性:")
    for feature in FEATURES:
        print(f"  • {feature}")
    print()
    
    print("💻 系统要求:")
    print(f"  Python版本: {REQUIREMENTS['python_version']}")
    print("  依赖库:")
    for dep in REQUIREMENTS['dependencies']:
        print(f"    - {dep}")
    print("  支持平台:")
    for platform in REQUIREMENTS['platforms']:
        print(f"    - {platform}")
    print()
    
    print("📁 支持的文件格式:")
    print("  配置文件:")
    for fmt in SUPPORTED_FORMATS['config_files']:
        print(f"    - {fmt}")
    print("  资源文件:")
    for fmt in SUPPORTED_FORMATS['resource_files']:
        print(f"    - {fmt}")
    print()
    
    print("📝 版本历史:")
    for version_info in VERSION_HISTORY:
        print(f"  v{version_info['version']} ({version_info['date']}):")
        for change in version_info['changes']:
            print(f"    {change}")
    print()
    
    print("=" * 60)


if __name__ == "__main__":
    print_version_info()
