@echo off
chcp 65001 >nul
echo 启动光效资源提取工具...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Python，请先安装Python 3.6或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 运行光效提取工具
python effect_extractor.py

REM 如果程序异常退出，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo 程序异常退出，错误代码：%errorlevel%
    echo 请检查Python环境和依赖库是否正确安装
    pause
)
