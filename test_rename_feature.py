#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按帧重命名功能
"""

import os
import tempfile
import shutil
from effect_extractor import EffectExtractor


def test_rename_mapping():
    """测试重命名映射生成"""
    print("=" * 60)
    print("测试重命名映射生成")
    print("=" * 60)
    
    extractor = EffectExtractor()
    
    # 模拟光效配置
    class MockEffectConfig:
        def getint(self, key, default=0):
            config_data = {
                'Amount': 3,
                'EffectId0': 111070,
                'TextureId0': 116978,
                'ASB0': 5, 'ADB0': 2, 'ZTest0': 1, 'Lev0': 1,
                'EffectId1': 111071,
                'TextureId1': 116979,
                'ASB1': 5, 'ADB1': 2, 'ZTest1': 1, 'Lev1': 1,
                'EffectId2': 111072,
                'TextureId2': 116980,
                'TextureId2_1': 117094,  # 辅助贴图
                'ASB2': 5, 'ADB2': 2, 'ZTest2': 1, 'Lev2': 1,
                'Delay': 0,
                'LoopTime': 99999999,
                'FrameInterval': 33,
                'LoopInterval': 0,
                'OffsetX': 0, 'OffsetY': 0, 'OffsetZ': 0,
                'ManualUVStep': 0
            }
            return config_data.get(key, default)
    
    mock_config = MockEffectConfig()
    
    # 模拟文件路径
    model_paths = [
        'c3/effect/interface/test_effect/model1.c3',
        'c3/effect/interface/test_effect/model2.c3',
        'c3/effect/interface/test_effect/model3.c3'
    ]
    
    texture_paths = [
        'c3/effect/interface/test_effect/texture1.png',
        'c3/effect/interface/test_effect/texture2.png',
        'c3/effect/interface/test_effect/texture3.png',
        'c3/effect/interface/test_effect/texture3_aux.png'  # 辅助贴图
    ]
    
    all_paths = model_paths + texture_paths
    
    print("原始文件路径:")
    print("模型文件:")
    for i, path in enumerate(model_paths, 1):
        print(f"  {i}. {os.path.basename(path)}")
    
    print("贴图文件:")
    for i, path in enumerate(texture_paths, 1):
        print(f"  {i}. {os.path.basename(path)}")
    
    # 生成重命名映射
    rename_mapping = extractor._generate_rename_mapping(all_paths, model_paths, mock_config)
    
    print(f"\n生成的重命名映射 ({len(rename_mapping)} 个):")
    for original_path, new_name in rename_mapping.items():
        original_name = os.path.basename(original_path)
        file_type = "模型" if original_path in model_paths else "贴图"
        print(f"  {file_type}: {original_name} -> {new_name}")
    
    # 验证重命名规则
    print(f"\n验证重命名规则:")
    
    # 检查模型文件命名
    model_renames = {path: name for path, name in rename_mapping.items() if path in model_paths}
    expected_model_names = ['1.c3', '2.c3', '3.c3']
    actual_model_names = [model_renames.get(path, os.path.basename(path)) for path in model_paths]
    
    if actual_model_names == expected_model_names:
        print("  ✅ 模型文件重命名正确")
    else:
        print(f"  ❌ 模型文件重命名错误: 期望 {expected_model_names}, 实际 {actual_model_names}")
    
    # 检查主贴图文件命名
    main_texture_paths = texture_paths[:3]  # 前3个是主贴图
    texture_renames = {path: name for path, name in rename_mapping.items() if path in main_texture_paths}
    expected_texture_names = ['1.png', '2.png', '3.png']
    actual_texture_names = [texture_renames.get(path, os.path.basename(path)) for path in main_texture_paths]
    
    if actual_texture_names == expected_texture_names:
        print("  ✅ 主贴图文件重命名正确")
    else:
        print(f"  ❌ 主贴图文件重命名错误: 期望 {expected_texture_names}, 实际 {actual_texture_names}")
    
    # 检查辅助贴图文件命名
    aux_texture_paths = texture_paths[3:]  # 第4个是辅助贴图
    if aux_texture_paths:
        aux_texture_renames = {path: name for path, name in rename_mapping.items() if path in aux_texture_paths}
        expected_aux_names = ['3_1.png']  # 第3帧的辅助贴图
        actual_aux_names = [aux_texture_renames.get(path, os.path.basename(path)) for path in aux_texture_paths]
        
        if actual_aux_names == expected_aux_names:
            print("  ✅ 辅助贴图文件重命名正确")
        else:
            print(f"  ❌ 辅助贴图文件重命名错误: 期望 {expected_aux_names}, 实际 {actual_aux_names}")
    
    print()


def test_real_effect_rename():
    """测试真实光效的重命名"""
    print("=" * 60)
    print("测试真实光效重命名")
    print("=" * 60)
    
    extractor = EffectExtractor()
    client_path = "客户端"
    effect_title = "smgx1_pkyh24_zsyj"
    
    print(f"测试光效: {effect_title}")
    
    try:
        # 查找光效配置
        effect_config, found_file = extractor.find_effect_config(client_path, effect_title)
        
        if effect_config:
            print(f"✅ 找到光效配置在 {found_file}")
            
            # 解析参数
            params = extractor.parse_effect_params(effect_config)
            
            if params:
                print(f"✅ 解析参数成功")
                print(f"   Amount: {params['amount']}")
                print(f"   EffectIds: {len(params['effect_ids'])} 个")
                print(f"   TextureIds: {len(params['texture_ids'])} 个")
                print(f"   TextureIds_1: {len(params['texture_ids_1'])} 个")
                
                # 获取资源路径
                model_paths, texture_paths = extractor.get_resource_paths(
                    client_path, params['effect_ids'], params['texture_ids'], params['texture_ids_1']
                )
                
                print(f"✅ 找到资源路径")
                print(f"   模型文件: {len(model_paths)} 个")
                print(f"   贴图文件: {len(texture_paths)} 个")
                
                # 生成重命名映射
                all_paths = model_paths + texture_paths
                rename_mapping = extractor._generate_rename_mapping(all_paths, model_paths, effect_config)
                
                print(f"\n重命名映射 ({len(rename_mapping)} 个文件):")
                
                # 显示模型文件重命名
                print("模型文件:")
                for i, model_path in enumerate(model_paths, 1):
                    original_name = os.path.basename(model_path)
                    new_name = rename_mapping.get(model_path, original_name)
                    print(f"  第{i}帧: {original_name} -> {new_name}")
                
                # 显示主贴图文件重命名
                main_texture_count = len(params['texture_ids'])
                print("主贴图文件:")
                for i, texture_path in enumerate(texture_paths[:main_texture_count], 1):
                    original_name = os.path.basename(texture_path)
                    new_name = rename_mapping.get(texture_path, original_name)
                    print(f"  第{i}帧: {original_name} -> {new_name}")
                
                # 显示辅助贴图文件重命名
                if len(texture_paths) > main_texture_count:
                    print("辅助贴图文件:")
                    aux_textures = texture_paths[main_texture_count:]
                    for i, texture_path in enumerate(aux_textures, 1):
                        original_name = os.path.basename(texture_path)
                        new_name = rename_mapping.get(texture_path, original_name)
                        print(f"  第{i}帧辅助: {original_name} -> {new_name}")
                
            else:
                print("❌ 参数解析失败")
        else:
            print("❌ 未找到光效配置")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    print()


def test_complete_extraction_with_rename():
    """测试完整提取流程（包含重命名）"""
    print("=" * 60)
    print("测试完整提取流程（包含重命名）")
    print("=" * 60)
    
    # 创建临时输出目录
    output_dir = tempfile.mkdtemp(prefix="rename_test_")
    
    try:
        extractor = EffectExtractor()
        client_path = "客户端"
        effect_title = "smgx1_pkyh24_zsyj"
        
        print(f"测试光效: {effect_title}")
        print(f"输出目录: {output_dir}")
        
        # 执行提取
        success, result = extractor.extract_effect(
            client_path, output_dir, effect_title
        )
        
        if success:
            print(f"✅ 提取成功! 复制了 {len(result)} 个文件")
            
            # 检查输出目录
            effect_output_dir = os.path.join(output_dir, '客户端更新', effect_title)
            if os.path.exists(effect_output_dir):
                files = os.listdir(effect_output_dir)
                print(f"\n输出文件列表 ({len(files)} 个):")
                
                # 分类显示文件
                model_files = [f for f in files if f.endswith('.c3')]
                texture_files = [f for f in files if f.endswith('.png')]
                config_files = [f for f in files if f.endswith('.ini')]
                
                print("模型文件:")
                for f in sorted(model_files):
                    print(f"  {f}")
                
                print("贴图文件:")
                for f in sorted(texture_files):
                    print(f"  {f}")
                
                print("配置文件:")
                for f in sorted(config_files):
                    print(f"  {f}")
                
                # 验证重命名是否正确
                print(f"\n验证重命名结果:")
                
                # 检查是否有按帧命名的文件
                frame_models = [f for f in model_files if f.split('.')[0].isdigit()]
                frame_textures = [f for f in texture_files if f.split('.')[0].isdigit() or '_1.' in f]
                
                if frame_models:
                    print(f"  ✅ 找到按帧命名的模型文件: {len(frame_models)} 个")
                else:
                    print(f"  ⚠️ 未找到按帧命名的模型文件")
                
                if frame_textures:
                    print(f"  ✅ 找到按帧命名的贴图文件: {len(frame_textures)} 个")
                else:
                    print(f"  ⚠️ 未找到按帧命名的贴图文件")
            else:
                print("❌ 输出目录不存在")
        else:
            print(f"❌ 提取失败: {result}")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n测试目录: {output_dir}")
        print("请手动检查输出结果")
        # 不自动删除，让用户可以检查结果


if __name__ == "__main__":
    print("光效资源提取工具 - 按帧重命名功能测试")
    print("=" * 60)
    
    test_rename_mapping()
    
    # 询问是否测试真实光效
    choice = input("是否测试真实光效重命名? (y/n): ").strip().lower()
    if choice == 'y':
        test_real_effect_rename()
        
        # 询问是否进行完整提取测试
        choice2 = input("是否进行完整提取测试? (y/n): ").strip().lower()
        if choice2 == 'y':
            test_complete_extraction_with_rename()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n📝 重命名规则:")
    print("  • 模型文件: 1.c3, 2.c3, 3.c3, ...")
    print("  • 主贴图: 1.png, 2.png, 3.png, ...")
    print("  • 辅助贴图: 1_1.png, 2_1.png, 3_1.png, ...")
    print("  • 配置文件: [光效名称].ini")
