# 光效重复键名检查工具 - 快速使用指南

## 🚀 快速开始

### 方法一：双击运行（推荐）
1. 双击 `run_duplicate_check.bat`
2. 按提示选择检查方式：
   - 直接回车：检查默认目录 `H:\DailyUpdate`
   - 输入 `detail`：详细模式检查默认目录
   - 输入目录路径：检查指定目录

### 方法二：命令行运行
```bash
# 检查默认目录
python check_duplicate_keys.py

# 检查指定目录
python check_duplicate_keys.py "D:\GameClient\Effects"

# 详细模式
python check_duplicate_keys.py --detail

# 指定目录 + 详细模式
python check_duplicate_keys.py "D:\GameClient\Effects" --detail
```

## 📊 输出说明

### 控制台输出
- **检查进度**：显示当前检查的文件
- **检查摘要**：统计信息和问题文件列表
- **详细信息**：重复键名的具体位置（详细模式）

### 生成的报告文件
- 文件名：`重复键名检查报告_YYYYMMDD_HHMMSS.txt`
- 内容：完整的检查结果和详细信息
- 位置：脚本运行目录

## 🎯 检查结果解读

### 正常情况
```
🎉 检查完成！未发现重复键名问题
```

### 发现问题
```
⚠️ 发现 3 个文件有重复键名问题
详细信息请查看生成的报告文件
```

### 重复键名示例
```
发现重复键名的文件:
  1. 3deffect2.ini - 2 个光效有重复键名
     [smgx1_pkyh24_zsyj]: TextureId1, ASB1
     [red0]: TextureId0
```

## ⚠️ 注意事项

1. **目录权限**：确保对检查目录有读取权限
2. **H盘检查**：如果H盘不存在，请指定其他目录
3. **大目录**：检查大量文件时可能需要较长时间
4. **编码问题**：INI文件需要使用GBK编码

## 🔧 常见问题

**Q: 提示目录不存在**
A: 检查目录路径是否正确，或指定其他存在的目录

**Q: 检查速度慢**
A: 正常现象，大量文件需要时间处理

**Q: 编码错误**
A: 确保INI文件使用GBK编码保存

## 📋 文件说明

- `check_duplicate_keys.py` - 主检查脚本
- `run_duplicate_check.bat` - 快速启动脚本
- `重复键名检查工具说明.md` - 详细说明文档

---

**快速提示**：首次使用建议先用详细模式了解工具输出格式！
