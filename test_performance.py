#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本
"""

import time
import tempfile
import os
from effect_extractor import EffectExtractor


def test_single_effect_performance():
    """测试单个光效提取性能"""
    print("=" * 60)
    print("单个光效提取性能测试")
    print("=" * 60)
    
    extractor = EffectExtractor()
    client_path = "客户端"
    effect_title = "smgx1_pkyh24_zsyj"
    
    print(f"测试光效: {effect_title}")
    
    # 测试配置解析性能
    start_time = time.time()
    effect_config, found_file = extractor.find_effect_config(client_path, effect_title)
    config_time = time.time() - start_time
    print(f"✅ 配置解析耗时: {config_time:.3f}秒")
    
    if effect_config:
        # 测试参数解析性能
        start_time = time.time()
        params = extractor.parse_effect_params(effect_config)
        params_time = time.time() - start_time
        print(f"✅ 参数解析耗时: {params_time:.3f}秒")
        
        if params:
            # 测试资源路径获取性能
            start_time = time.time()
            model_paths, texture_paths = extractor.get_resource_paths(
                client_path, params['effect_ids'], params['texture_ids'], params['texture_ids_1']
            )
            paths_time = time.time() - start_time
            print(f"✅ 资源路径获取耗时: {paths_time:.3f}秒")
            
            total_time = config_time + params_time + paths_time
            print(f"📊 总耗时: {total_time:.3f}秒")
            print(f"📊 找到资源: {len(model_paths)} 个模型, {len(texture_paths)} 个贴图")
    
    print()


def test_batch_performance():
    """测试批量处理性能"""
    print("=" * 60)
    print("批量处理性能测试")
    print("=" * 60)
    
    # 创建测试title文件
    test_dir = tempfile.mkdtemp(prefix="perf_test_")
    title_file = os.path.join(test_dir, "test_titles.txt")
    
    # 创建包含多个光效的测试文件
    test_titles = [
        "smgx1_pkyh24_zsyj",
        "red0",
        "green0", 
        "combo0",
        "nonexistent1",  # 不存在的光效
        "nonexistent2"
    ]
    
    with open(title_file, 'w', encoding='utf-8') as f:
        f.write("# 性能测试光效列表\n")
        for title in test_titles:
            f.write(f"{title}\n")
    
    print(f"测试文件: {title_file}")
    print(f"测试光效数量: {len(test_titles)}")
    
    extractor = EffectExtractor()
    client_path = "客户端"
    output_path = test_dir
    
    # 测试批量处理性能
    start_time = time.time()
    
    def progress_callback(message):
        elapsed = time.time() - start_time
        print(f"[{elapsed:.1f}s] {message}")
    
    success, result = extractor.extract_effects_from_file(
        client_path, output_path, title_file, progress_callback
    )
    
    total_time = time.time() - start_time
    
    if success:
        print(f"\n✅ 批量处理完成")
        print(f"📊 总耗时: {total_time:.3f}秒")
        print(f"📊 平均每个光效: {total_time/len(test_titles):.3f}秒")
        print(f"📊 成功: {len(result['success'])} 个")
        print(f"📊 失败: {len(result['failed'])} 个")
        
        # 分析性能
        if len(result['success']) > 0:
            avg_success_time = total_time / len(result['success'])
            print(f"📊 成功光效平均耗时: {avg_success_time:.3f}秒")
    else:
        print(f"❌ 批量处理失败: {result}")
    
    # 清理
    import shutil
    shutil.rmtree(test_dir)
    print(f"已清理测试目录: {test_dir}")
    print()


def test_cache_performance():
    """测试缓存性能"""
    print("=" * 60)
    print("缓存性能测试")
    print("=" * 60)
    
    extractor = EffectExtractor()
    client_path = "客户端"
    
    # 测试资源映射缓存
    ini_dir = os.path.join(client_path, 'ini')
    effect_obj_ini = os.path.join(ini_dir, '3deffectobj.ini')
    texture_ini = os.path.join(ini_dir, '3dtexture.ini')
    
    if os.path.exists(effect_obj_ini):
        print("测试模型映射缓存...")
        
        # 第一次加载（无缓存）
        start_time = time.time()
        extractor._parse_resource_mapping_cached(effect_obj_ini, [111070, 111071], "模型")
        first_load_time = time.time() - start_time
        print(f"✅ 首次加载耗时: {first_load_time:.3f}秒")
        
        # 第二次加载（有缓存）
        start_time = time.time()
        extractor._parse_resource_mapping_cached(effect_obj_ini, [111072, 111073], "模型")
        cached_load_time = time.time() - start_time
        print(f"✅ 缓存加载耗时: {cached_load_time:.3f}秒")
        
        if first_load_time > 0:
            speedup = first_load_time / cached_load_time if cached_load_time > 0 else float('inf')
            print(f"📊 缓存加速比: {speedup:.1f}x")
    
    if os.path.exists(texture_ini):
        print("\n测试贴图映射缓存...")
        
        # 第一次加载（无缓存）
        start_time = time.time()
        extractor._parse_resource_mapping_cached(texture_ini, [116978, 116979], "贴图")
        first_load_time = time.time() - start_time
        print(f"✅ 首次加载耗时: {first_load_time:.3f}秒")
        
        # 第二次加载（有缓存）
        start_time = time.time()
        extractor._parse_resource_mapping_cached(texture_ini, [116980, 116981], "贴图")
        cached_load_time = time.time() - start_time
        print(f"✅ 缓存加载耗时: {cached_load_time:.3f}秒")
        
        if first_load_time > 0:
            speedup = first_load_time / cached_load_time if cached_load_time > 0 else float('inf')
            print(f"📊 缓存加速比: {speedup:.1f}x")
    
    print()


def test_memory_usage():
    """测试内存使用情况"""
    print("=" * 60)
    print("内存使用测试")
    print("=" * 60)
    
    try:
        import psutil
        process = psutil.Process()
        
        # 初始内存
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"初始内存使用: {initial_memory:.1f} MB")
        
        # 创建提取器
        extractor = EffectExtractor()
        after_init_memory = process.memory_info().rss / 1024 / 1024
        print(f"创建提取器后: {after_init_memory:.1f} MB (+{after_init_memory-initial_memory:.1f} MB)")
        
        # 预加载缓存
        client_path = "客户端"
        extractor._preload_resource_mappings(client_path)
        after_cache_memory = process.memory_info().rss / 1024 / 1024
        print(f"预加载缓存后: {after_cache_memory:.1f} MB (+{after_cache_memory-after_init_memory:.1f} MB)")
        
        # 检查缓存大小
        cache_count = len(extractor._mapping_cache)
        print(f"缓存文件数量: {cache_count}")
        
        if cache_count > 0:
            total_mappings = sum(len(mapping) for mapping in extractor._mapping_cache.values())
            print(f"缓存映射总数: {total_mappings}")
            avg_memory_per_mapping = (after_cache_memory - after_init_memory) / total_mappings if total_mappings > 0 else 0
            print(f"平均每个映射内存: {avg_memory_per_mapping*1024:.1f} KB")
        
    except ImportError:
        print("⚠️ 未安装psutil，跳过内存测试")
        print("安装命令: pip install psutil")
    
    print()


def performance_summary():
    """性能总结"""
    print("=" * 60)
    print("性能优化总结")
    print("=" * 60)
    
    print("🚀 已实现的优化:")
    print("  ✅ 资源映射文件缓存 - 避免重复解析大文件")
    print("  ✅ 批量文件复制优化 - 减少系统调用")
    print("  ✅ 预加载机制 - 批量处理时提前加载缓存")
    print("  ✅ 快速字符串查找 - 优化INI文件解析")
    print("  ✅ 内存优化 - 减少不必要的对象创建")
    
    print("\n📊 预期性能提升:")
    print("  • 单个光效提取: 20-50% 速度提升")
    print("  • 批量处理: 50-80% 速度提升")
    print("  • 内存使用: 稳定，不随光效数量线性增长")
    
    print("\n💡 使用建议:")
    print("  • 批量处理多个光效时性能提升最明显")
    print("  • 首次运行会稍慢（需要建立缓存）")
    print("  • 建议一次性处理相关的光效")


if __name__ == "__main__":
    print("光效资源提取工具 - 性能测试")
    print("=" * 60)
    
    test_single_effect_performance()
    test_cache_performance()
    test_memory_usage()
    
    # 询问是否进行批量测试
    choice = input("是否进行批量处理性能测试? (y/n): ").strip().lower()
    if choice == 'y':
        test_batch_performance()
    
    performance_summary()
    
    print("\n" + "=" * 60)
    print("性能测试完成")
    print("=" * 60)
