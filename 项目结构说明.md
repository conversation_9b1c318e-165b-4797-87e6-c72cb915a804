# 光效资源提取工具 - 项目结构说明

## 📁 项目文件结构

```
光效提取工具/
├── effect_extractor.py          # 主程序文件（包含GUI界面和核心逻辑）
├── test_extractor.py            # 测试脚本
├── run_extractor.bat            # Windows启动脚本
├── README.md                    # 详细使用说明
├── requirements.txt             # 依赖说明（主要使用标准库）
├── example_config.ini           # INI配置文件格式示例
├── 项目结构说明.md              # 本文件
└── __pycache__/                 # Python缓存目录（自动生成）
```

## 🚀 快速开始

### 方法1：使用批处理脚本（推荐）
双击 `run_extractor.bat` 文件即可启动程序

### 方法2：命令行启动
```bash
python effect_extractor.py
```

### 方法3：运行测试
```bash
python test_extractor.py
```

## 📋 文件说明

### 核心文件

#### `effect_extractor.py`
- **主程序文件**，包含完整的功能实现
- 包含两个主要类：
  - `EffectExtractor`: 核心提取逻辑
  - `EffectExtractorGUI`: 图形用户界面
- 功能模块：
  - INI文件解析
  - 光效配置读取
  - 资源路径映射
  - 文件复制和目录组织
  - 日志记录和错误处理

#### `test_extractor.py`
- **测试脚本**，用于验证工具功能
- 包含测试用例：
  - INI文件解析测试
  - 光效提取流程测试
  - 错误处理测试
- 自动创建测试环境和清理

### 辅助文件

#### `run_extractor.bat`
- **Windows启动脚本**
- 自动检查Python环境
- 提供友好的错误提示
- 支持中文显示

#### `README.md`
- **详细使用说明**
- 功能介绍和工作原理
- 安装和使用步骤
- 故障排除指南

#### `example_config.ini`
- **配置文件格式示例**
- 展示3deffect2.ini格式
- 包含参数说明和注释
- 提供多个光效配置示例

#### `requirements.txt`
- **依赖说明文件**
- 主要使用Python标准库
- 包含可选依赖的安装说明
- 不同系统的tkinter安装指导

## 🔧 技术架构

### 核心类设计

```python
EffectExtractor
├── parse_ini_file()           # INI文件解析
├── find_effect_config()       # 查找光效配置
├── parse_effect_params()      # 解析光效参数
├── get_resource_paths()       # 获取资源路径
├── copy_resources()           # 复制资源文件
└── extract_effect()           # 主提取流程

EffectExtractorGUI
├── setup_gui()               # 界面初始化
├── setup_logging_redirect()  # 日志重定向
├── browse_client_path()      # 路径选择
├── update_progress()         # 进度更新
└── start_extraction()        # 启动提取
```

### 数据流程

```
用户输入 → GUI界面 → 参数验证 → 
INI解析 → 配置查找 → 参数解析 → 
路径映射 → 文件复制 → 结果反馈
```

## 📊 支持的文件格式

### INI配置文件
- `3deffect2.ini` - 光效配置文件2
- `3deffect3.ini` - 光效配置文件3  
- `3deffectobj.ini` - 模型路径映射
- `3dtexture.ini` - 贴图路径映射

### 资源文件
- `.c3` - 3D模型文件
- `.tga` - 贴图文件
- 其他游戏资源文件

## 🎯 输出目录结构

```
输出目录/
└── 客户端更新/
    ├── [光效名称1]/
    │   ├── 1.c3                    # 模型文件
    │   ├── 2.c3
    │   ├── 1.png                   # 贴图文件
    │   ├── 2.png
    │   ├── 6_1.png                 # 辅助贴图
    │   └── [光效名称1].ini         # 配置文件
    └── [光效名称2]/
        ├── 1.c3
        ├── 2.c3
        ├── 1.png
        ├── 2.png
        └── [光效名称2].ini
```

**重要说明**：
- 🎯 **简化层级**：直接在"客户端更新"目录下创建光效文件夹
- 📁 **统一存放**：模型文件(.c3)和贴图文件(.png)都在同一目录下
- 📄 **配置保存**：每个光效目录包含完整的配置文件

## 🛠️ 开发和扩展

### 添加新功能
1. 在`EffectExtractor`类中添加新方法
2. 在`EffectExtractorGUI`类中添加对应的界面元素
3. 更新测试脚本验证新功能

### 自定义配置
- 修改`parse_effect_params()`方法支持新的参数类型
- 调整`copy_resources()`方法改变输出结构
- 扩展`get_resource_paths()`方法支持新的资源类型

### 错误处理
- 所有关键操作都包含try-catch块
- 详细的日志记录便于调试
- 用户友好的错误提示

## 📝 使用注意事项

1. **编码格式**：INI文件使用GBK编码
2. **路径格式**：自动处理Windows/Unix路径分隔符
3. **权限要求**：确保对输出目录有写入权限
4. **文件覆盖**：同名文件会被自动覆盖

## 🔍 故障排除

### 常见问题
1. **Python环境**：确保安装Python 3.6+
2. **tkinter缺失**：参考requirements.txt安装说明
3. **编码错误**：检查INI文件编码格式
4. **路径错误**：确认客户端目录结构正确

### 调试方法
1. 查看日志输出区域的详细信息
2. 运行测试脚本验证基本功能
3. 检查临时文件和输出目录

## 📞 技术支持

如遇到问题，请：
1. 查看日志输出中的错误信息
2. 运行测试脚本检查环境
3. 参考README.md中的故障排除部分
4. 检查项目文件是否完整
