# 光效资源提取工具 - 更新日志

## 🚀 v1.3.0 (2025-08-06) - 目录结构简化版本

### 🔧 重大改进

#### 1. 目录结构简化
- ✅ **简化输出层级**：直接在"客户端更新"目录下创建光效文件夹
- ✅ **统一文件存放**：模型、贴图、配置文件都在同一目录下
- ✅ **减少嵌套层级**：去除了 `c3/effect/interface/` 的多层嵌套

#### 2. 新的输出结构
```
输出目录/
└── 客户端更新/
    ├── [光效title1]/
    │   ├── 1.c3                    # 模型文件
    │   ├── 2.c3
    │   ├── 1.png                   # 贴图文件
    │   ├── 2.png
    │   └── [光效title1].ini        # 配置文件
    └── [光效title2]/
        ├── 1.c3
        ├── 2.c3
        ├── 1.png
        ├── 2.png
        └── [光效title2].ini
```

#### 3. 优势特点
- 🎯 **便于管理**：每个光效一个文件夹，结构清晰
- 📁 **减少层级**：从5层目录减少到2层目录
- 🚀 **提高效率**：更快的文件访问和操作速度
- 💾 **节省空间**：减少目录结构占用的磁盘空间

---

## 🚀 v1.2.0 (2025-08-06) - 配置保存和批量处理版本

### 🔧 重大改进

#### 1. 配置保存功能
- ✅ **自动保存配置**：每次提取时自动保存光效完整配置到ini文件
- ✅ **完整参数记录**：包含所有解析到的参数
- ✅ **UTF-8编码**：支持中文注释和内容

#### 2. 批量处理功能
- ✅ **txt文件批量输入**：支持从txt文件读取多个光效title
- ✅ **注释支持**：支持 `#` 开头的注释行
- ✅ **批量报告**：自动生成详细的处理结果报告
- ✅ **GUI界面升级**：新增批量模式选择

---

## 🚀 v1.1.0 (2025-08-06) - 真实客户端适配版本

### 🔧 重大改进

#### 1. 真实客户端配置支持
- ✅ **完全适配真实游戏客户端**：基于实际客户端配置文件进行优化
- ✅ **处理复杂光效参数**：支持 `TextureId_1`、`MixOpt` 等高级参数
- ✅ **智能资源映射**：正确解析无section格式的映射文件

#### 2. 强化INI文件解析
- ✅ **重复键名处理**：自动处理INI文件中的重复键名问题
- ✅ **多层解析策略**：标准解析 → 宽松模式 → 手动解析
- ✅ **错误恢复机制**：即使遇到格式问题也能继续工作

#### 3. 扩展参数支持
- ✅ **TextureId_1 支持**：处理辅助贴图资源
- ✅ **MixOpt 参数**：支持混合选项配置
- ✅ **动态参数解析**：自动识别和处理新的参数类型

#### 4. 文件格式兼容性
- ✅ **多格式贴图**：支持 .png、.tga 等多种贴图格式
- ✅ **路径格式处理**：正确处理Windows/Unix路径分隔符
- ✅ **编码兼容性**：支持GBK编码的中文配置文件

### 📊 测试验证

#### 真实配置测试结果
```
光效名称: smgx1_pkyh24_zsyj
✅ 成功解析 7 个光效资源
✅ 找到 7 个模型文件 (.c3)
✅ 找到 9 个贴图文件 (.png，包括辅助贴图)
✅ 正确处理 TextureId_1 和 MixOpt 参数
✅ 自动跳过重复键名问题
```

#### 解析的参数类型
- **基础参数**：Amount, EffectId0-6, TextureId0-6
- **扩展参数**：TextureId5_1, TextureId6_1, MixOpt5, MixOpt6
- **控制参数**：ASB, ADB, ZTest, Lev, Delay, LoopTime等

### 🔍 发现的实际配置格式

#### 光效配置示例 (3deffect3.ini)
```ini
[smgx1_pkyh24_zsyj]
Amount=7
EffectId0=111070
TextureId0=116978
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
...
EffectId5=111168
TextureId5=117093
TextureId5_1=117094  # 辅助贴图
MixOpt5=28           # 混合选项
...
```

#### 资源映射格式 (3deffectobj.ini)
```ini
111070=c3/effect/interface/smgx1_pkyh24_zsyj/1.c3
111071=c3/effect/interface/smgx1_pkyh24_zsyj/2.c3
...
```

#### 贴图映射格式 (3dtexture.ini)
```ini
116978=c3/effect/interface/smgx1_pkyh24_zsyj/1.png
116979=c3/effect/interface/smgx1_pkyh24_zsyj/2.png
117094=c3/effect/interface/smgx1_pkyh24_zsyj/6_1.png  # 辅助贴图
...
```

### 🛠️ 技术改进

#### 1. 新增解析方法
```python
def _parse_resource_mapping(self, ini_file, resource_ids, resource_type):
    """解析资源映射文件 - 支持无section格式"""
    
def _manual_parse_ini(self, file_path, encoding='gbk'):
    """手动解析有问题的INI文件 - 处理重复键名"""
```

#### 2. 扩展参数结构
```python
params = {
    'texture_ids_1': [],  # 新增：辅助贴图ID
    'mix_opts': [],       # 新增：混合选项
    # ... 其他参数
}
```

#### 3. 智能错误处理
- 重复键名自动跳过
- 多种解析策略自动切换
- 详细的错误日志和警告信息

### 📁 输出结构优化

#### 统一目录结构
```
输出目录/
└── 客户端更新/
    └── c3/
        └── effect/
            └── interface/
                └── smgx1_pkyh24_zsyj/
                    ├── 1.c3              # 模型文件
                    ├── 2.c3
                    ├── ...
                    ├── 7.c3
                    ├── 1.png             # 主贴图
                    ├── 2.png
                    ├── ...
                    ├── 7.png
                    ├── 6_1.png           # 辅助贴图
                    └── 7_1.png
```

### 🎯 使用建议

#### 1. 对于真实客户端
- 直接使用，工具已完全适配
- 注意查看日志中的警告信息
- 确认所有资源文件都已正确提取

#### 2. 对于测试环境
- 使用 `test_real_client.py` 验证配置解析
- 检查文件是否存在于客户端目录中
- 确认路径映射是否正确

#### 3. 故障排除
- 查看详细日志输出
- 检查INI文件编码格式
- 确认客户端目录结构完整

### 🔄 向后兼容性

- ✅ 完全兼容之前的配置格式
- ✅ 保持原有的API接口不变
- ✅ 支持新旧两种参数格式

### 📞 技术支持

如果遇到问题：
1. 运行 `python test_real_client.py` 进行诊断
2. 查看日志输出中的详细错误信息
3. 确认客户端目录结构和文件完整性
4. 检查INI文件格式和编码

---

## 📋 v1.0.0 (2025-08-06) - 初始版本

### 基础功能
- 光效资源提取
- 图形化界面
- INI文件解析
- 目录结构组织
- 错误处理机制
