#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的性能测试脚本
"""

import time
import tempfile
import os
from effect_extractor import EffectExtractor


def test_optimized_single_effect():
    """测试优化后的单个光效提取性能"""
    print("=" * 60)
    print("优化后单个光效提取性能测试")
    print("=" * 60)
    
    extractor = EffectExtractor()
    client_path = "客户端"
    effect_title = "smgx1_pkyh24_zsyj"
    
    print(f"测试光效: {effect_title}")
    
    # 测试完整提取流程
    start_time = time.time()
    
    # 1. 查找光效配置（包含索引构建）
    config_start = time.time()
    effect_config, found_file = extractor.find_effect_config(client_path, effect_title)
    config_time = time.time() - config_start
    print(f"✅ 光效配置查找耗时: {config_time:.3f}秒")
    
    if effect_config:
        # 2. 解析光效参数
        params_start = time.time()
        params = extractor.parse_effect_params(effect_config)
        params_time = time.time() - params_start
        print(f"✅ 参数解析耗时: {params_time:.3f}秒")
        
        if params:
            # 3. 获取资源路径（使用缓存）
            paths_start = time.time()
            model_paths, texture_paths = extractor.get_resource_paths(
                client_path, params['effect_ids'], params['texture_ids'], params['texture_ids_1']
            )
            paths_time = time.time() - paths_start
            print(f"✅ 资源路径获取耗时: {paths_time:.3f}秒")
            
            total_time = time.time() - start_time
            print(f"📊 总耗时: {total_time:.3f}秒")
            print(f"📊 找到资源: {len(model_paths)} 个模型, {len(texture_paths)} 个贴图")
            
            # 测试第二次查找（应该更快）
            print(f"\n测试缓存效果（第二次查找）:")
            second_start = time.time()
            effect_config2, found_file2 = extractor.find_effect_config(client_path, effect_title)
            if effect_config2:
                params2 = extractor.parse_effect_params(effect_config2)
                if params2:
                    model_paths2, texture_paths2 = extractor.get_resource_paths(
                        client_path, params2['effect_ids'], params2['texture_ids'], params2['texture_ids_1']
                    )
            second_time = time.time() - second_start
            print(f"✅ 第二次查找耗时: {second_time:.3f}秒")
            
            if total_time > 0 and second_time > 0:
                speedup = total_time / second_time
                print(f"📊 缓存加速比: {speedup:.1f}x")
    
    print()


def test_optimized_batch_performance():
    """测试优化后的批量处理性能"""
    print("=" * 60)
    print("优化后批量处理性能测试")
    print("=" * 60)
    
    # 创建测试title文件
    test_dir = tempfile.mkdtemp(prefix="optimized_perf_test_")
    title_file = os.path.join(test_dir, "test_titles.txt")
    
    # 创建包含多个光效的测试文件
    test_titles = [
        "smgx1_pkyh24_zsyj",
        "red0",
        "green0", 
        "combo0",
        "nonexistent1",  # 不存在的光效
        "nonexistent2",
        "smgx1_pkyh24_zsgj"  # 如果存在的话
    ]
    
    with open(title_file, 'w', encoding='utf-8') as f:
        f.write("# 优化性能测试光效列表\n")
        for title in test_titles:
            f.write(f"{title}\n")
    
    print(f"测试文件: {title_file}")
    print(f"测试光效数量: {len(test_titles)}")
    
    extractor = EffectExtractor()
    client_path = "客户端"
    output_path = test_dir
    
    # 测试批量处理性能
    start_time = time.time()
    
    def progress_callback(message):
        elapsed = time.time() - start_time
        print(f"[{elapsed:.1f}s] {message}")
    
    success, result = extractor.extract_effects_from_file(
        client_path, output_path, title_file, progress_callback
    )
    
    total_time = time.time() - start_time
    
    if success:
        print(f"\n✅ 批量处理完成")
        print(f"📊 总耗时: {total_time:.3f}秒")
        print(f"📊 平均每个光效: {total_time/len(test_titles):.3f}秒")
        print(f"📊 成功: {len(result['success'])} 个")
        print(f"📊 失败: {len(result['failed'])} 个")
        
        # 分析性能
        if len(result['success']) > 0:
            avg_success_time = total_time / len(result['success'])
            print(f"📊 成功光效平均耗时: {avg_success_time:.3f}秒")
            
            # 计算吞吐量
            throughput = len(result['success']) / total_time
            print(f"📊 处理吞吐量: {throughput:.2f} 光效/秒")
    else:
        print(f"❌ 批量处理失败: {result}")
    
    # 清理
    import shutil
    shutil.rmtree(test_dir)
    print(f"已清理测试目录: {test_dir}")
    print()


def test_index_performance():
    """测试索引构建性能"""
    print("=" * 60)
    print("光效索引构建性能测试")
    print("=" * 60)
    
    extractor = EffectExtractor()
    client_path = "客户端"
    
    # 测试索引构建
    print("测试光效索引构建...")
    start_time = time.time()
    extractor._build_effect_index(client_path)
    build_time = time.time() - start_time
    
    print(f"✅ 索引构建耗时: {build_time:.3f}秒")
    print(f"✅ 索引光效数量: {len(extractor._effect_index)}")
    
    if len(extractor._effect_index) > 0:
        avg_time_per_effect = build_time / len(extractor._effect_index)
        print(f"📊 平均每个光效索引时间: {avg_time_per_effect*1000:.2f}毫秒")
    
    # 测试索引查找性能
    print(f"\n测试索引查找性能...")
    test_effects = ["smgx1_pkyh24_zsyj", "red0", "green0", "nonexistent"]
    
    for effect in test_effects:
        start_time = time.time()
        if effect in extractor._effect_index:
            effect_info = extractor._effect_index[effect]
            result = f"找到在 {effect_info['file']}"
        else:
            result = "未找到"
        search_time = time.time() - start_time
        print(f"  {effect}: {result} ({search_time*1000:.2f}毫秒)")
    
    print()


def test_memory_efficiency():
    """测试内存效率"""
    print("=" * 60)
    print("内存效率测试")
    print("=" * 60)
    
    try:
        import psutil
        process = psutil.Process()
        
        # 初始内存
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"初始内存使用: {initial_memory:.1f} MB")
        
        # 创建提取器
        extractor = EffectExtractor()
        after_init_memory = process.memory_info().rss / 1024 / 1024
        print(f"创建提取器后: {after_init_memory:.1f} MB (+{after_init_memory-initial_memory:.1f} MB)")
        
        # 构建索引
        client_path = "客户端"
        extractor._build_effect_index(client_path)
        after_index_memory = process.memory_info().rss / 1024 / 1024
        print(f"构建索引后: {after_index_memory:.1f} MB (+{after_index_memory-after_init_memory:.1f} MB)")
        
        # 预加载缓存
        extractor._preload_resource_mappings(client_path)
        after_cache_memory = process.memory_info().rss / 1024 / 1024
        print(f"预加载缓存后: {after_cache_memory:.1f} MB (+{after_cache_memory-after_index_memory:.1f} MB)")
        
        # 分析缓存效率
        index_count = len(extractor._effect_index)
        cache_count = len(extractor._mapping_cache)
        
        print(f"\n📊 缓存统计:")
        print(f"  光效索引数量: {index_count}")
        print(f"  映射缓存文件数: {cache_count}")
        
        if cache_count > 0:
            total_mappings = sum(len(mapping) for mapping in extractor._mapping_cache.values())
            print(f"  总映射记录数: {total_mappings}")
            
            cache_memory = after_cache_memory - after_index_memory
            if total_mappings > 0:
                memory_per_mapping = cache_memory * 1024 / total_mappings  # KB
                print(f"  平均每个映射内存: {memory_per_mapping:.2f} KB")
        
        total_memory = after_cache_memory - initial_memory
        print(f"\n📊 总内存开销: {total_memory:.1f} MB")
        
    except ImportError:
        print("⚠️ 未安装psutil，跳过内存测试")
        print("安装命令: pip install psutil")
    
    print()


def performance_comparison():
    """性能对比总结"""
    print("=" * 60)
    print("性能优化对比总结")
    print("=" * 60)
    
    print("🚀 实施的优化措施:")
    print("  ✅ 光效配置索引 - 使用正则表达式快速构建索引")
    print("  ✅ 资源映射缓存 - 正则表达式批量解析")
    print("  ✅ 并行文件复制 - 多线程并行处理")
    print("  ✅ 智能预加载 - 并行构建索引和缓存")
    print("  ✅ 高效数据结构 - 字典查找替代线性搜索")
    
    print("\n📊 预期性能提升:")
    print("  • INI文件解析: 50-80% 速度提升")
    print("  • 光效查找: 90%+ 速度提升（索引查找）")
    print("  • 资源映射: 70-90% 速度提升")
    print("  • 文件复制: 30-50% 速度提升（并行）")
    print("  • 批量处理: 60-85% 速度提升")
    
    print("\n💡 优化原理:")
    print("  • 减少磁盘I/O: 一次读取，多次使用")
    print("  • 优化算法: O(n) -> O(1) 查找复杂度")
    print("  • 并行处理: 充分利用多核CPU")
    print("  • 智能缓存: 避免重复计算")


if __name__ == "__main__":
    print("光效资源提取工具 - 优化后性能测试")
    print("=" * 60)
    
    test_index_performance()
    test_optimized_single_effect()
    test_memory_efficiency()
    
    # 询问是否进行批量测试
    choice = input("是否进行批量处理性能测试? (y/n): ").strip().lower()
    if choice == 'y':
        test_optimized_batch_performance()
    
    performance_comparison()
    
    print("\n" + "=" * 60)
    print("优化后性能测试完成")
    print("=" * 60)
