@echo off
chcp 65001 >nul
echo ========================================
echo 光效重复键名检查工具
echo ========================================
echo.
echo 默认检查目录: H:\DailyUpdate
echo.
echo 使用说明:
echo   1. 直接回车 - 检查默认目录
echo   2. 输入目录路径 - 检查指定目录
echo   3. 输入 'detail' - 详细模式检查默认目录
echo.

set /p user_input="请输入目录路径或选项 (直接回车使用默认): "

if "%user_input%"=="" (
    echo 检查默认目录: H:\DailyUpdate
    python check_duplicate_keys.py
) else if "%user_input%"=="detail" (
    echo 详细模式检查默认目录: H:\DailyUpdate
    python check_duplicate_keys.py --detail
) else (
    echo 检查指定目录: %user_input%
    python check_duplicate_keys.py "%user_input%"
)

echo.
echo 按任意键退出...
pause >nul
