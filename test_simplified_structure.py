#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的目录结构
"""

import os
import tempfile
import shutil
from effect_extractor import EffectExtractor


def test_simplified_structure():
    """测试简化的目录结构"""
    print("=" * 60)
    print("测试简化的目录结构")
    print("=" * 60)
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="simplified_test_")
    
    try:
        # 创建提取器实例
        extractor = EffectExtractor()
        
        # 模拟光效配置
        effect_config = {
            'Amount': '3',
            'EffectId0': '111070',
            'TextureId0': '116978',
            'ASB0': '5',
            'ADB0': '2',
            'ZTest0': '1',
            'Lev0': '1',
            'EffectId1': '111071',
            'TextureId1': '116979',
            'ASB1': '5',
            'ADB1': '2',
            'ZTest1': '1',
            'Lev1': '1',
            'EffectId2': '111072',
            'TextureId2': '116980',
            'ASB2': '5',
            'ADB2': '2',
            'ZTest2': '1',
            'Lev2': '1',
            'Delay': '0',
            'LoopTime': '99999999',
            'FrameInterval': '33',
            'LoopInterval': '0',
            'OffsetX': '0',
            'OffsetY': '0',
            'OffsetZ': '0',
            'ManualUVStep': '0'
        }
        
        # 模拟资源路径
        model_paths = [
            'c3/effect/interface/test_effect/1.c3',
            'c3/effect/interface/test_effect/2.c3',
            'c3/effect/interface/test_effect/3.c3'
        ]
        
        texture_paths = [
            'c3/effect/interface/test_effect/1.png',
            'c3/effect/interface/test_effect/2.png',
            'c3/effect/interface/test_effect/3.png'
        ]
        
        # 创建模拟的源文件
        client_dir = os.path.join(test_dir, "client")
        for path in model_paths + texture_paths:
            full_path = os.path.join(client_dir, path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(f"# Test file: {os.path.basename(path)}\n")
        
        # 测试复制资源
        effect_title = "test_effect"
        output_dir = os.path.join(test_dir, "output")
        
        print(f"测试光效: {effect_title}")
        print(f"输出目录: {output_dir}")
        
        copied_files = extractor.copy_resources(
            client_dir, output_dir, effect_title, model_paths, texture_paths, effect_config
        )
        
        print(f"\n✅ 复制了 {len(copied_files)} 个文件")
        
        # 验证目录结构
        expected_output_dir = os.path.join(output_dir, '客户端更新', effect_title)
        
        if os.path.exists(expected_output_dir):
            print("✅ 输出目录创建成功")
            print(f"   路径: {expected_output_dir}")
            
            # 列出所有文件
            files = os.listdir(expected_output_dir)
            print(f"\n📁 目录内容 ({len(files)} 个文件):")
            for i, filename in enumerate(sorted(files), 1):
                file_path = os.path.join(expected_output_dir, filename)
                file_size = os.path.getsize(file_path)
                print(f"   {i}. {filename} ({file_size} bytes)")
            
            # 验证预期文件
            expected_files = ['1.c3', '2.c3', '3.c3', '1.png', '2.png', '3.png', f'{effect_title}.ini']
            missing_files = []
            extra_files = []
            
            for expected in expected_files:
                if expected not in files:
                    missing_files.append(expected)
            
            for actual in files:
                if actual not in expected_files:
                    extra_files.append(actual)
            
            if not missing_files and not extra_files:
                print("✅ 文件列表验证成功")
            else:
                if missing_files:
                    print(f"❌ 缺少文件: {missing_files}")
                if extra_files:
                    print(f"⚠️ 额外文件: {extra_files}")
            
            # 验证配置文件内容
            config_file = os.path.join(expected_output_dir, f'{effect_title}.ini')
            if os.path.exists(config_file):
                print("\n✅ 配置文件创建成功")
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print("配置文件内容预览:")
                print("-" * 30)
                lines = content.split('\n')[:10]  # 只显示前10行
                for line in lines:
                    print(f"   {line}")
                if len(content.split('\n')) > 10:
                    print("   ...")
                print("-" * 30)
            else:
                print("❌ 配置文件未创建")
        else:
            print("❌ 输出目录未创建")
        
        print(f"\n📊 目录结构验证:")
        print(f"   预期路径: 输出目录/客户端更新/{effect_title}/")
        print(f"   实际路径: {os.path.relpath(expected_output_dir, output_dir)}")
        print(f"   层级简化: ✅ 减少了 c3/effect/interface/ 层级")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n测试目录: {test_dir}")
        print("请手动检查测试结果")
        # 不自动删除，让用户可以检查结果
        # shutil.rmtree(test_dir)


def test_multiple_effects():
    """测试多个光效的目录结构"""
    print("\n" + "=" * 60)
    print("测试多个光效的目录结构")
    print("=" * 60)
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="multi_effects_test_")
    
    try:
        # 创建提取器实例
        extractor = EffectExtractor()
        
        # 测试多个光效
        effects = [
            {
                'title': 'effect1',
                'models': ['1.c3', '2.c3'],
                'textures': ['1.png', '2.png']
            },
            {
                'title': 'effect2', 
                'models': ['1.c3', '2.c3', '3.c3'],
                'textures': ['1.png', '2.png', '3.png', '1_1.png']
            },
            {
                'title': 'smgx1_test',
                'models': ['1.c3'],
                'textures': ['1.png']
            }
        ]
        
        output_dir = os.path.join(test_dir, "output")
        
        for effect in effects:
            print(f"\n处理光效: {effect['title']}")
            
            # 模拟配置
            config = {
                'Amount': str(len(effect['models'])),
                'Delay': '0',
                'LoopTime': '99999999'
            }
            
            # 模拟路径
            model_paths = [f"c3/effect/interface/{effect['title']}/{model}" for model in effect['models']]
            texture_paths = [f"c3/effect/interface/{effect['title']}/{texture}" for texture in effect['textures']]
            
            # 复制资源
            copied_files = extractor.copy_resources(
                test_dir, output_dir, effect['title'], model_paths, texture_paths, config
            )
            
            print(f"   复制了 {len(copied_files)} 个文件")
        
        # 验证整体结构
        client_update_dir = os.path.join(output_dir, '客户端更新')
        if os.path.exists(client_update_dir):
            print(f"\n📁 客户端更新目录结构:")
            subdirs = [d for d in os.listdir(client_update_dir) if os.path.isdir(os.path.join(client_update_dir, d))]
            
            for i, subdir in enumerate(sorted(subdirs), 1):
                subdir_path = os.path.join(client_update_dir, subdir)
                files = os.listdir(subdir_path)
                print(f"   {i}. {subdir}/ ({len(files)} 个文件)")
                for file in sorted(files):
                    print(f"      - {file}")
            
            print(f"\n✅ 成功创建 {len(subdirs)} 个光效目录")
            print("✅ 每个光效都有独立的目录")
            print("✅ 目录结构简化，便于管理")
        else:
            print("❌ 客户端更新目录未创建")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n测试目录: {test_dir}")
        print("请手动检查测试结果")


if __name__ == "__main__":
    test_simplified_structure()
    test_multiple_effects()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
