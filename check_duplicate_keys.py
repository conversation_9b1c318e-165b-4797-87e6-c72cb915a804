#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光效重复键名检查工具
检查指定目录下所有光效配置文件中的重复键名问题
"""

import os
import sys
import configparser
import logging
from pathlib import Path
import time


class DuplicateKeyChecker:
    def __init__(self):
        self.logger = self._setup_logger()
        self.duplicate_effects = []
        self.total_effects = 0
        self.total_files = 0
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('DuplicateKeyChecker')
        logger.setLevel(logging.INFO)
        
        # 控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def check_ini_file(self, ini_file_path):
        """检查单个INI文件中的重复键名"""
        duplicates = []
        
        try:
            # 首先尝试标准解析
            config = configparser.ConfigParser()
            config.read(ini_file_path, encoding='gbk')
            
            # 如果能正常解析，说明没有重复键名
            return duplicates
            
        except configparser.DuplicateOptionError as e:
            # 捕获重复键名错误
            self.logger.info(f"发现重复键名文件: {ini_file_path}")
            
            # 手动解析文件以找出所有重复键名
            duplicates = self._find_duplicate_keys_manual(ini_file_path)
            
        except Exception as e:
            self.logger.error(f"解析文件失败 {ini_file_path}: {e}")
            
        return duplicates
    
    def _find_duplicate_keys_manual(self, ini_file_path):
        """手动解析文件找出重复键名"""
        duplicates = []
        
        try:
            with open(ini_file_path, 'r', encoding='gbk') as f:
                lines = f.readlines()
            
            current_section = None
            section_keys = {}
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # 跳过空行和注释
                if not line or line.startswith('#') or line.startswith('//'):
                    continue
                
                # 检查是否是section
                if line.startswith('[') and line.endswith(']'):
                    current_section = line[1:-1]
                    section_keys[current_section] = {}
                    continue
                
                # 解析键值对
                if '=' in line and current_section:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    
                    # 检查是否重复
                    if key in section_keys[current_section]:
                        duplicate_info = {
                            'section': current_section,
                            'key': key,
                            'line': line_num,
                            'first_line': section_keys[current_section][key],
                            'value': value.strip()
                        }
                        duplicates.append(duplicate_info)
                    else:
                        section_keys[current_section][key] = line_num
                        
        except Exception as e:
            self.logger.error(f"手动解析文件失败 {ini_file_path}: {e}")
            
        return duplicates
    
    def check_directory(self, directory_path):
        """检查目录下的所有INI文件"""
        self.logger.info(f"开始检查目录: {directory_path}")
        
        if not os.path.exists(directory_path):
            self.logger.error(f"目录不存在: {directory_path}")
            return
        
        # 查找所有INI文件
        ini_files = []
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.lower().endswith('.ini'):
                    ini_files.append(os.path.join(root, file))
        
        self.total_files = len(ini_files)
        self.logger.info(f"找到 {self.total_files} 个INI文件")
        
        if self.total_files == 0:
            self.logger.warning("未找到任何INI文件")
            return
        
        # 检查每个文件
        for i, ini_file in enumerate(ini_files, 1):
            self.logger.info(f"[{i}/{self.total_files}] 检查文件: {os.path.basename(ini_file)}")
            
            duplicates = self.check_ini_file(ini_file)
            
            if duplicates:
                effect_info = {
                    'file': ini_file,
                    'duplicates': duplicates
                }
                self.duplicate_effects.append(effect_info)
                
                # 统计重复键名的section数量
                sections_with_duplicates = set(dup['section'] for dup in duplicates)
                self.total_effects += len(sections_with_duplicates)
    
    def generate_report(self, output_file=None):
        """生成检查报告"""
        if output_file is None:
            output_file = f"重复键名检查报告_{time.strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("光效重复键名检查报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"检查文件总数: {self.total_files}\n")
                f.write(f"有重复键名的文件数: {len(self.duplicate_effects)}\n")
                f.write(f"有重复键名的光效数: {self.total_effects}\n")
                f.write("\n")
                
                if not self.duplicate_effects:
                    f.write("🎉 恭喜！未发现任何重复键名问题。\n")
                else:
                    f.write("发现的重复键名问题:\n")
                    f.write("-" * 80 + "\n")
                    
                    for i, effect_info in enumerate(self.duplicate_effects, 1):
                        f.write(f"\n{i}. 文件: {effect_info['file']}\n")
                        
                        # 按section分组显示重复键名
                        sections = {}
                        for dup in effect_info['duplicates']:
                            section = dup['section']
                            if section not in sections:
                                sections[section] = []
                            sections[section].append(dup)
                        
                        for section, dups in sections.items():
                            f.write(f"   光效: [{section}]\n")
                            
                            # 按键名分组
                            keys = {}
                            for dup in dups:
                                key = dup['key']
                                if key not in keys:
                                    keys[key] = []
                                keys[key].append(dup)
                            
                            for key, key_dups in keys.items():
                                f.write(f"     重复键名: {key}\n")
                                for dup in key_dups:
                                    f.write(f"       第{dup['line']}行: {key}={dup['value']}\n")
                                    f.write(f"       (首次出现在第{dup['first_line']}行)\n")
                            f.write("\n")
                
                f.write("=" * 80 + "\n")
                f.write("检查完成\n")
                f.write("=" * 80 + "\n")
            
            self.logger.info(f"报告已保存到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
    
    def print_summary(self):
        """打印检查摘要"""
        print("\n" + "=" * 80)
        print("检查摘要")
        print("=" * 80)
        print(f"检查文件总数: {self.total_files}")
        print(f"有重复键名的文件数: {len(self.duplicate_effects)}")
        print(f"有重复键名的光效数: {self.total_effects}")

        if self.duplicate_effects:
            print(f"\n发现重复键名的文件:")
            for i, effect_info in enumerate(self.duplicate_effects, 1):
                filename = os.path.basename(effect_info['file'])
                sections = set(dup['section'] for dup in effect_info['duplicates'])
                print(f"  {i}. {filename} - {len(sections)} 个光效有重复键名")

                # 显示具体的重复键名
                for section in sorted(sections):
                    section_dups = [dup for dup in effect_info['duplicates'] if dup['section'] == section]
                    keys = set(dup['key'] for dup in section_dups)
                    print(f"     [{section}]: {', '.join(sorted(keys))}")
        else:
            print("\n🎉 恭喜！未发现任何重复键名问题。")

        print("=" * 80)

    def print_detailed_duplicates(self):
        """打印详细的重复键名信息"""
        if not self.duplicate_effects:
            return

        print("\n" + "=" * 80)
        print("详细重复键名信息")
        print("=" * 80)

        for i, effect_info in enumerate(self.duplicate_effects, 1):
            print(f"\n{i}. 文件: {effect_info['file']}")

            # 按section分组显示重复键名
            sections = {}
            for dup in effect_info['duplicates']:
                section = dup['section']
                if section not in sections:
                    sections[section] = []
                sections[section].append(dup)

            for section, dups in sections.items():
                print(f"   光效: [{section}]")

                # 按键名分组
                keys = {}
                for dup in dups:
                    key = dup['key']
                    if key not in keys:
                        keys[key] = []
                    keys[key].append(dup)

                for key, key_dups in keys.items():
                    print(f"     重复键名: {key}")
                    for dup in key_dups:
                        print(f"       第{dup['line']}行: {key}={dup['value']}")
                        print(f"       (首次出现在第{dup['first_line']}行)")
                print()

        print("=" * 80)


def main():
    """主函数"""
    print("光效重复键名检查工具")
    print("=" * 80)

    # 默认检查目录
    default_directory = r"H:\DailyUpdate"

    # 检查命令行参数
    check_directory = default_directory
    show_details = False

    for arg in sys.argv[1:]:
        if arg == "--detail" or arg == "-d":
            show_details = True
        elif not arg.startswith("-"):
            check_directory = arg

    print(f"检查目录: {check_directory}")
    if show_details:
        print("详细模式: 开启")

    # 创建检查器
    checker = DuplicateKeyChecker()

    # 开始检查
    start_time = time.time()
    checker.check_directory(check_directory)
    end_time = time.time()

    # 打印摘要
    checker.print_summary()

    # 如果有重复键名且开启详细模式，显示详细信息
    if show_details and checker.duplicate_effects:
        checker.print_detailed_duplicates()

    # 生成报告
    checker.generate_report()

    print(f"\n检查耗时: {end_time - start_time:.2f} 秒")

    if checker.duplicate_effects:
        print(f"\n⚠️  发现 {len(checker.duplicate_effects)} 个文件有重复键名问题")
        print("详细信息请查看生成的报告文件")
        if not show_details:
            print("使用 --detail 参数可在控制台显示详细信息")
    else:
        print("\n🎉 检查完成！未发现重复键名问题")

    print("\n" + "=" * 80)


if __name__ == "__main__":
    main()
