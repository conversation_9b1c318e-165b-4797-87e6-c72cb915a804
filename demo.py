#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光效资源提取工具演示脚本
展示如何使用EffectExtractor类进行编程式调用
"""

import os
import tempfile
from effect_extractor import EffectExtractor


def demo_programmatic_usage():
    """演示编程式使用方法"""
    print("=" * 60)
    print("光效资源提取工具 - 编程式使用演示")
    print("=" * 60)
    
    # 创建提取器实例
    extractor = EffectExtractor()
    
    print("\n1. 创建EffectExtractor实例")
    print("   extractor = EffectExtractor()")
    
    print("\n2. 设置参数")
    client_path = input("请输入客户端根目录路径: ").strip()
    output_path = input("请输入输出目录路径: ").strip()
    effect_title = input("请输入光效title名称: ").strip()
    
    if not client_path or not output_path or not effect_title:
        print("❌ 参数不完整，演示结束")
        return
    
    print(f"   客户端路径: {client_path}")
    print(f"   输出路径: {output_path}")
    print(f"   光效名称: {effect_title}")
    
    print("\n3. 执行提取")
    print("   success, result = extractor.extract_effect(client_path, output_path, effect_title)")
    
    # 定义进度回调函数
    def progress_callback(message):
        print(f"   进度: {message}")
    
    # 执行提取
    success, result = extractor.extract_effect(
        client_path, output_path, effect_title, progress_callback
    )
    
    print("\n4. 处理结果")
    if success:
        print("✅ 提取成功!")
        print(f"   复制的文件数量: {len(result)}")
        print("   复制的文件列表:")
        for i, file_path in enumerate(result, 1):
            print(f"     {i}. {file_path}")
    else:
        print("❌ 提取失败!")
        print(f"   错误信息: {result}")
    
    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)


def demo_batch_extraction():
    """演示批量提取多个光效"""
    print("\n" + "=" * 60)
    print("批量提取演示")
    print("=" * 60)
    
    # 创建提取器实例
    extractor = EffectExtractor()
    
    client_path = input("请输入客户端根目录路径: ").strip()
    output_path = input("请输入输出目录路径: ").strip()
    
    if not client_path or not output_path:
        print("❌ 路径不完整，演示结束")
        return
    
    # 要提取的光效列表
    effect_list_input = input("请输入光效名称列表（用逗号分隔）: ").strip()
    if not effect_list_input:
        print("❌ 光效列表为空，演示结束")
        return
    
    effect_list = [name.strip() for name in effect_list_input.split(',')]
    
    print(f"\n准备提取 {len(effect_list)} 个光效:")
    for i, effect in enumerate(effect_list, 1):
        print(f"  {i}. {effect}")
    
    print("\n开始批量提取...")
    
    success_count = 0
    failed_count = 0
    
    for i, effect_title in enumerate(effect_list, 1):
        print(f"\n[{i}/{len(effect_list)}] 提取光效: {effect_title}")
        print("-" * 40)
        
        success, result = extractor.extract_effect(
            client_path, output_path, effect_title
        )
        
        if success:
            success_count += 1
            print(f"✅ {effect_title} 提取成功! 复制了 {len(result)} 个文件")
        else:
            failed_count += 1
            print(f"❌ {effect_title} 提取失败: {result}")
    
    print("\n" + "=" * 60)
    print("批量提取完成")
    print(f"成功: {success_count} 个")
    print(f"失败: {failed_count} 个")
    print("=" * 60)


def demo_ini_analysis():
    """演示INI文件分析功能"""
    print("\n" + "=" * 60)
    print("INI文件分析演示")
    print("=" * 60)
    
    extractor = EffectExtractor()
    
    client_path = input("请输入客户端根目录路径: ").strip()
    if not client_path:
        print("❌ 路径为空，演示结束")
        return
    
    ini_dir = os.path.join(client_path, 'ini')
    if not os.path.exists(ini_dir):
        print(f"❌ INI目录不存在: {ini_dir}")
        return
    
    print(f"\n分析INI目录: {ini_dir}")
    
    # 分析3deffect2.ini
    effect2_path = os.path.join(ini_dir, '3deffect2.ini')
    if os.path.exists(effect2_path):
        print(f"\n📄 分析文件: 3deffect2.ini")
        config = extractor.parse_ini_file(effect2_path)
        if config:
            sections = config.sections()
            print(f"   找到 {len(sections)} 个光效配置:")
            for i, section in enumerate(sections[:10], 1):  # 只显示前10个
                print(f"     {i}. {section}")
            if len(sections) > 10:
                print(f"     ... 还有 {len(sections) - 10} 个")
        else:
            print("   ❌ 解析失败")
    
    # 分析3deffect3.ini
    effect3_path = os.path.join(ini_dir, '3deffect3.ini')
    if os.path.exists(effect3_path):
        print(f"\n📄 分析文件: 3deffect3.ini")
        config = extractor.parse_ini_file(effect3_path)
        if config:
            sections = config.sections()
            print(f"   找到 {len(sections)} 个光效配置:")
            for i, section in enumerate(sections[:10], 1):  # 只显示前10个
                print(f"     {i}. {section}")
            if len(sections) > 10:
                print(f"     ... 还有 {len(sections) - 10} 个")
        else:
            print("   ❌ 解析失败")
    
    # 分析特定光效
    effect_name = input("\n请输入要详细分析的光效名称（可选）: ").strip()
    if effect_name:
        print(f"\n🔍 详细分析光效: {effect_name}")
        effect_config, found_file = extractor.find_effect_config(client_path, effect_name)
        
        if effect_config:
            print(f"   ✅ 在 {found_file} 中找到配置")
            params = extractor.parse_effect_params(effect_config)
            
            if params:
                print(f"   Amount: {params['amount']}")
                print(f"   EffectIds: {params['effect_ids']}")
                print(f"   TextureIds: {params['texture_ids']}")
                print(f"   Delay: {params['delay']}")
                print(f"   LoopTime: {params['loop_time']}")
                print(f"   FrameInterval: {params['frame_interval']}")
                
                # 获取资源路径
                model_paths, texture_paths = extractor.get_resource_paths(
                    client_path, params['effect_ids'], params['texture_ids']
                )
                
                print(f"\n   模型文件路径:")
                for i, path in enumerate(model_paths, 1):
                    print(f"     {i}. {path}")
                
                print(f"\n   贴图文件路径:")
                for i, path in enumerate(texture_paths, 1):
                    print(f"     {i}. {path}")
            else:
                print("   ❌ 参数解析失败")
        else:
            print("   ❌ 未找到配置")


def main():
    """主函数"""
    print("光效资源提取工具 - 演示脚本")
    print("请选择演示模式:")
    print("1. 编程式使用演示")
    print("2. 批量提取演示")
    print("3. INI文件分析演示")
    print("4. 启动GUI界面")
    print("0. 退出")
    
    while True:
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            demo_programmatic_usage()
        elif choice == '2':
            demo_batch_extraction()
        elif choice == '3':
            demo_ini_analysis()
        elif choice == '4':
            print("启动GUI界面...")
            from effect_extractor import EffectExtractorGUI
            app = EffectExtractorGUI()
            app.run()
        elif choice == '0':
            print("退出演示")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()
