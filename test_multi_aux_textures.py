#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多张辅助贴图重命名功能
"""

import os
import tempfile
import shutil
from effect_extractor import EffectExtractor


def test_multi_aux_texture_parsing():
    """测试多张辅助贴图解析"""
    print("=" * 60)
    print("测试多张辅助贴图解析")
    print("=" * 60)
    
    extractor = EffectExtractor()
    
    # 模拟包含多张辅助贴图的光效配置
    class MockEffectConfig:
        def getint(self, key, default=0):
            config_data = {
                'Amount': 3,
                # 第0帧
                'EffectId0': 111070,
                'TextureId0': 116978,
                'ASB0': 5, 'ADB0': 2, 'ZTest0': 1, 'Lev0': 1,
                # 第1帧
                'EffectId1': 111071,
                'TextureId1': 116979,
                'ASB1': 5, 'ADB1': 2, 'ZTest1': 1, 'Lev1': 1,
                # 第2帧 - 包含多张辅助贴图
                'EffectId2': 111072,
                'TextureId2': 116980,
                'TextureId2_1': 117094,  # 第1张辅助贴图
                'TextureId2_2': 117095,  # 第2张辅助贴图
                'TextureId2_3': 117096,  # 第3张辅助贴图
                'ASB2': 5, 'ADB2': 2, 'ZTest2': 1, 'Lev2': 1,
                # 其他参数
                'Delay': 0,
                'LoopTime': 99999999,
                'FrameInterval': 33,
                'LoopInterval': 0,
                'OffsetX': 0, 'OffsetY': 0, 'OffsetZ': 0,
                'ManualUVStep': 0
            }
            return config_data.get(key, default)
    
    mock_config = MockEffectConfig()
    
    # 解析参数
    params = extractor.parse_effect_params(mock_config)
    
    if params:
        print("✅ 参数解析成功")
        print(f"Amount: {params['amount']}")
        print(f"EffectIds: {params['effect_ids']}")
        print(f"TextureIds: {params['texture_ids']}")
        print(f"TextureIds_1: {params['texture_ids_1']}")
        print(f"TextureIds_2: {params['texture_ids_2']}")
        print(f"TextureIds_3: {params['texture_ids_3']}")
        print(f"TextureIds_4: {params['texture_ids_4']}")
        
        # 验证解析结果
        expected_texture_ids_1 = [117094]
        expected_texture_ids_2 = [117095]
        expected_texture_ids_3 = [117096]
        
        if params['texture_ids_1'] == expected_texture_ids_1:
            print("  ✅ TextureIds_1 解析正确")
        else:
            print(f"  ❌ TextureIds_1 解析错误: 期望 {expected_texture_ids_1}, 实际 {params['texture_ids_1']}")
        
        if params['texture_ids_2'] == expected_texture_ids_2:
            print("  ✅ TextureIds_2 解析正确")
        else:
            print(f"  ❌ TextureIds_2 解析错误: 期望 {expected_texture_ids_2}, 实际 {params['texture_ids_2']}")
        
        if params['texture_ids_3'] == expected_texture_ids_3:
            print("  ✅ TextureIds_3 解析正确")
        else:
            print(f"  ❌ TextureIds_3 解析错误: 期望 {expected_texture_ids_3}, 实际 {params['texture_ids_3']}")
    else:
        print("❌ 参数解析失败")
    
    print()


def test_multi_aux_texture_rename():
    """测试多张辅助贴图重命名"""
    print("=" * 60)
    print("测试多张辅助贴图重命名")
    print("=" * 60)
    
    extractor = EffectExtractor()
    
    # 模拟光效配置
    class MockEffectConfig:
        def getint(self, key, default=0):
            config_data = {
                'Amount': 3,
                # 第0帧
                'EffectId0': 111070,
                'TextureId0': 116978,
                'ASB0': 5, 'ADB0': 2, 'ZTest0': 1, 'Lev0': 1,
                # 第1帧
                'EffectId1': 111071,
                'TextureId1': 116979,
                'ASB1': 5, 'ADB1': 2, 'ZTest1': 1, 'Lev1': 1,
                # 第2帧 - 包含多张辅助贴图
                'EffectId2': 111072,
                'TextureId2': 116980,
                'TextureId2_1': 117094,  # 第1张辅助贴图
                'TextureId2_2': 117095,  # 第2张辅助贴图
                'TextureId2_3': 117096,  # 第3张辅助贴图
                'ASB2': 5, 'ADB2': 2, 'ZTest2': 1, 'Lev2': 1,
                # 其他参数
                'Delay': 0,
                'LoopTime': 99999999,
                'FrameInterval': 33,
                'LoopInterval': 0,
                'OffsetX': 0, 'OffsetY': 0, 'OffsetZ': 0,
                'ManualUVStep': 0
            }
            return config_data.get(key, default)
    
    mock_config = MockEffectConfig()
    
    # 模拟文件路径
    model_paths = [
        'c3/effect/interface/test_effect/model1.c3',
        'c3/effect/interface/test_effect/model2.c3',
        'c3/effect/interface/test_effect/model3.c3'
    ]
    
    texture_paths = [
        # 主贴图
        'c3/effect/interface/test_effect/texture1.png',
        'c3/effect/interface/test_effect/texture2.png',
        'c3/effect/interface/test_effect/texture3.png',
        # 辅助贴图
        'c3/effect/interface/test_effect/texture3_1.png',  # 第3帧第1张辅助贴图
        'c3/effect/interface/test_effect/texture3_2.png',  # 第3帧第2张辅助贴图
        'c3/effect/interface/test_effect/texture3_3.png'   # 第3帧第3张辅助贴图
    ]
    
    all_paths = model_paths + texture_paths
    
    print("原始文件路径:")
    print("模型文件:")
    for i, path in enumerate(model_paths, 1):
        print(f"  第{i}帧: {os.path.basename(path)}")
    
    print("主贴图文件:")
    for i, path in enumerate(texture_paths[:3], 1):
        print(f"  第{i}帧: {os.path.basename(path)}")
    
    print("辅助贴图文件:")
    for i, path in enumerate(texture_paths[3:], 1):
        print(f"  第3帧第{i}张: {os.path.basename(path)}")
    
    # 生成重命名映射
    rename_mapping = extractor._generate_rename_mapping(all_paths, model_paths, mock_config)
    
    print(f"\n生成的重命名映射 ({len(rename_mapping)} 个):")
    
    # 显示模型文件重命名
    print("模型文件重命名:")
    for path in model_paths:
        original_name = os.path.basename(path)
        new_name = rename_mapping.get(path, original_name)
        print(f"  {original_name} -> {new_name}")
    
    # 显示主贴图文件重命名
    print("主贴图文件重命名:")
    for path in texture_paths[:3]:
        original_name = os.path.basename(path)
        new_name = rename_mapping.get(path, original_name)
        print(f"  {original_name} -> {new_name}")
    
    # 显示辅助贴图文件重命名
    print("辅助贴图文件重命名:")
    for path in texture_paths[3:]:
        original_name = os.path.basename(path)
        new_name = rename_mapping.get(path, original_name)
        print(f"  {original_name} -> {new_name}")
    
    # 验证重命名规则
    print(f"\n验证重命名规则:")
    
    # 检查模型文件命名
    expected_model_names = ['1.c3', '2.c3', '3.c3']
    actual_model_names = [rename_mapping.get(path, os.path.basename(path)) for path in model_paths]
    
    if actual_model_names == expected_model_names:
        print("  ✅ 模型文件重命名正确")
    else:
        print(f"  ❌ 模型文件重命名错误: 期望 {expected_model_names}, 实际 {actual_model_names}")
    
    # 检查主贴图文件命名
    expected_texture_names = ['1.png', '2.png', '3.png']
    actual_texture_names = [rename_mapping.get(path, os.path.basename(path)) for path in texture_paths[:3]]
    
    if actual_texture_names == expected_texture_names:
        print("  ✅ 主贴图文件重命名正确")
    else:
        print(f"  ❌ 主贴图文件重命名错误: 期望 {expected_texture_names}, 实际 {actual_texture_names}")
    
    # 检查辅助贴图文件命名
    expected_aux_names = ['3_1.png', '3_2.png', '3_3.png']
    actual_aux_names = [rename_mapping.get(path, os.path.basename(path)) for path in texture_paths[3:]]
    
    if actual_aux_names == expected_aux_names:
        print("  ✅ 辅助贴图文件重命名正确")
    else:
        print(f"  ❌ 辅助贴图文件重命名错误: 期望 {expected_aux_names}, 实际 {actual_aux_names}")
    
    print()


def test_complex_multi_aux_scenario():
    """测试复杂的多张辅助贴图场景"""
    print("=" * 60)
    print("测试复杂的多张辅助贴图场景")
    print("=" * 60)
    
    print("场景描述:")
    print("  • 第1帧: 主贴图")
    print("  • 第2帧: 主贴图 + 1张辅助贴图")
    print("  • 第3帧: 主贴图 + 3张辅助贴图")
    print("  • 第4帧: 主贴图 + 2张辅助贴图")
    
    # 模拟复杂场景的文件路径
    model_paths = [
        'c3/effect/interface/complex/model1.c3',
        'c3/effect/interface/complex/model2.c3',
        'c3/effect/interface/complex/model3.c3',
        'c3/effect/interface/complex/model4.c3'
    ]
    
    texture_paths = [
        # 主贴图 (4帧)
        'c3/effect/interface/complex/texture1.png',
        'c3/effect/interface/complex/texture2.png',
        'c3/effect/interface/complex/texture3.png',
        'c3/effect/interface/complex/texture4.png',
        # 辅助贴图
        'c3/effect/interface/complex/texture2_1.png',  # 第2帧第1张辅助贴图
        'c3/effect/interface/complex/texture3_1.png',  # 第3帧第1张辅助贴图
        'c3/effect/interface/complex/texture3_2.png',  # 第3帧第2张辅助贴图
        'c3/effect/interface/complex/texture3_3.png',  # 第3帧第3张辅助贴图
        'c3/effect/interface/complex/texture4_1.png',  # 第4帧第1张辅助贴图
        'c3/effect/interface/complex/texture4_2.png'   # 第4帧第2张辅助贴图
    ]
    
    all_paths = model_paths + texture_paths
    
    print(f"\n原始文件 ({len(all_paths)} 个):")
    print("模型文件:")
    for i, path in enumerate(model_paths, 1):
        print(f"  第{i}帧: {os.path.basename(path)}")
    
    print("主贴图文件:")
    for i, path in enumerate(texture_paths[:4], 1):
        print(f"  第{i}帧: {os.path.basename(path)}")
    
    print("辅助贴图文件:")
    aux_textures = texture_paths[4:]
    for path in aux_textures:
        original_name = os.path.basename(path)
        print(f"  {original_name}")
    
    # 模拟复杂配置
    class ComplexMockConfig:
        def getint(self, key, default=0):
            config_data = {
                'Amount': 4,
                # 第0帧
                'EffectId0': 111070, 'TextureId0': 116978,
                # 第1帧 + 1张辅助贴图
                'EffectId1': 111071, 'TextureId1': 116979,
                'TextureId1_1': 117001,
                # 第2帧 + 3张辅助贴图
                'EffectId2': 111072, 'TextureId2': 116980,
                'TextureId2_1': 117002, 'TextureId2_2': 117003, 'TextureId2_3': 117004,
                # 第3帧 + 2张辅助贴图
                'EffectId3': 111073, 'TextureId3': 116981,
                'TextureId3_1': 117005, 'TextureId3_2': 117006,
            }
            return config_data.get(key, default)
    
    complex_config = ComplexMockConfig()
    
    extractor = EffectExtractor()
    rename_mapping = extractor._generate_rename_mapping(all_paths, model_paths, complex_config)
    
    print(f"\n重命名结果 ({len(rename_mapping)} 个文件):")
    
    # 显示所有重命名结果
    print("模型文件:")
    for path in model_paths:
        original = os.path.basename(path)
        renamed = rename_mapping.get(path, original)
        print(f"  {original} -> {renamed}")
    
    print("主贴图文件:")
    for path in texture_paths[:4]:
        original = os.path.basename(path)
        renamed = rename_mapping.get(path, original)
        print(f"  {original} -> {renamed}")
    
    print("辅助贴图文件:")
    for path in texture_paths[4:]:
        original = os.path.basename(path)
        renamed = rename_mapping.get(path, original)
        print(f"  {original} -> {renamed}")
    
    print()


if __name__ == "__main__":
    print("光效资源提取工具 - 多张辅助贴图重命名功能测试")
    print("=" * 60)
    
    test_multi_aux_texture_parsing()
    test_multi_aux_texture_rename()
    test_complex_multi_aux_scenario()
    
    print("=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n📝 多张辅助贴图重命名规则:")
    print("  • 主贴图: 1.png, 2.png, 3.png, ...")
    print("  • 第1张辅助贴图: 帧号_1.png (如: 2_1.png, 3_1.png)")
    print("  • 第2张辅助贴图: 帧号_2.png (如: 3_2.png, 4_2.png)")
    print("  • 第3张辅助贴图: 帧号_3.png (如: 3_3.png, 4_3.png)")
    print("  • 第4张辅助贴图: 帧号_4.png (如: 3_4.png, 4_4.png)")
    print("\n💡 支持的辅助贴图配置:")
    print("  • TextureId帧号_1 = 贴图ID")
    print("  • TextureId帧号_2 = 贴图ID")
    print("  • TextureId帧号_3 = 贴图ID")
    print("  • TextureId帧号_4 = 贴图ID")
