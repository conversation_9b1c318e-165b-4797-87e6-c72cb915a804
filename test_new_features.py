#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能：配置保存和批量处理
"""

import os
import tempfile
import shutil
from effect_extractor import EffectExtractor


def test_config_save():
    """测试配置保存功能"""
    print("=" * 60)
    print("测试配置保存功能")
    print("=" * 60)
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="config_test_")
    
    try:
        # 创建提取器实例
        extractor = EffectExtractor()
        
        # 模拟光效配置
        effect_config = {
            'Amount': '7',
            'EffectId0': '111070',
            'TextureId0': '116978',
            'ASB0': '5',
            'ADB0': '2',
            'ZTest0': '1',
            'Lev0': '1',
            'EffectId1': '111071',
            'TextureId1': '116979',
            'ASB1': '5',
            'ADB1': '2',
            'ZTest1': '1',
            'Lev1': '1',
            'Delay': '0',
            'LoopTime': '99999999',
            'FrameInterval': '33',
            'LoopInterval': '0',
            'OffsetX': '0',
            'OffsetY': '0',
            'OffsetZ': '0',
            'ManualUVStep': '0'
        }
        
        # 测试保存配置
        effect_title = "test_effect"
        extractor._save_effect_config(test_dir, effect_title, effect_config)
        
        # 检查文件是否创建
        config_file = os.path.join(test_dir, f"{effect_title}.ini")
        if os.path.exists(config_file):
            print("✅ 配置文件创建成功")
            
            # 读取并验证内容
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("配置文件内容:")
            print("-" * 30)
            print(content)
            print("-" * 30)
            
            # 验证关键内容
            if f"[{effect_title}]" in content and "Amount=7" in content:
                print("✅ 配置内容验证成功")
            else:
                print("❌ 配置内容验证失败")
        else:
            print("❌ 配置文件创建失败")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        shutil.rmtree(test_dir)
        print(f"已清理测试目录: {test_dir}")


def test_batch_processing():
    """测试批量处理功能"""
    print("\n" + "=" * 60)
    print("测试批量处理功能")
    print("=" * 60)
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="batch_test_")
    
    try:
        # 创建title列表文件
        title_file = os.path.join(test_dir, "test_titles.txt")
        with open(title_file, 'w', encoding='utf-8') as f:
            f.write("# 测试title列表\n")
            f.write("red0\n")
            f.write("green0\n")
            f.write("combo0\n")
            f.write("# 注释行\n")
            f.write("\n")  # 空行
            f.write("nonexistent_effect\n")  # 不存在的光效
        
        print(f"创建测试title文件: {title_file}")
        
        # 创建提取器实例
        extractor = EffectExtractor()
        
        # 测试批量处理
        client_path = "客户端"
        output_path = test_dir
        
        print(f"客户端路径: {client_path}")
        print(f"输出路径: {output_path}")
        
        # 执行批量提取
        success, result = extractor.extract_effects_from_file(
            client_path, output_path, title_file
        )
        
        if success:
            print("✅ 批量处理完成")
            print(f"总计: {result['total']} 个光效")
            print(f"成功: {len(result['success'])} 个")
            print(f"失败: {len(result['failed'])} 个")
            
            print("\n成功的光效:")
            for item in result['success']:
                print(f"  ✅ {item['title']} (复制了 {len(item['files'])} 个文件)")
            
            print("\n失败的光效:")
            for item in result['failed']:
                print(f"  ❌ {item['title']}: {item['error']}")
            
            # 检查报告文件
            report_file = os.path.join(output_path, '批量提取报告.txt')
            if os.path.exists(report_file):
                print("\n✅ 批量处理报告生成成功")
                with open(report_file, 'r', encoding='utf-8') as f:
                    print("报告内容:")
                    print("-" * 30)
                    print(f.read())
                    print("-" * 30)
            else:
                print("\n❌ 批量处理报告生成失败")
        else:
            print(f"❌ 批量处理失败: {result}")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n测试目录: {test_dir}")
        print("请手动检查测试结果")


def test_title_file_parsing():
    """测试title文件解析"""
    print("\n" + "=" * 60)
    print("测试title文件解析")
    print("=" * 60)
    
    # 创建临时文件
    test_dir = tempfile.mkdtemp(prefix="title_parse_test_")
    title_file = os.path.join(test_dir, "test_parse.txt")
    
    try:
        # 创建包含各种情况的title文件
        content = """# 这是注释行
title1
# 另一个注释
title2

title3
   title4   
# title5 (这行是注释)
title6

# 最后一个注释
title7"""
        
        with open(title_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("测试文件内容:")
        print("-" * 30)
        print(content)
        print("-" * 30)
        
        # 解析文件
        with open(title_file, 'r', encoding='utf-8') as f:
            titles = [line.strip() for line in f.readlines() if line.strip() and not line.strip().startswith('#')]
        
        print(f"解析结果: {titles}")
        
        expected = ['title1', 'title2', 'title3', 'title4', 'title6', 'title7']
        if titles == expected:
            print("✅ title文件解析正确")
        else:
            print(f"❌ title文件解析错误，期望: {expected}")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    finally:
        shutil.rmtree(test_dir)


if __name__ == "__main__":
    test_config_save()
    test_title_file_parsing()
    
    # 询问是否进行批量处理测试
    choice = input("\n是否进行批量处理测试? (需要客户端目录) (y/n): ").strip().lower()
    if choice == 'y':
        test_batch_processing()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
