# 光效重复键名检查工具

## 🎯 工具简介

这是一个专门用于检查光效配置文件中重复键名问题的工具。它会扫描指定目录下的所有INI文件，找出存在重复键名的光效配置，并生成详细的检查报告。

## ✨ 功能特点

- 🔍 **全目录扫描**：递归扫描指定目录下的所有INI文件
- 📋 **详细报告**：生成包含文件位置、光效名称、重复键名的详细报告
- ⚡ **快速检查**：高效的文件解析和重复键名检测
- 📊 **统计信息**：提供检查摘要和统计数据
- 💾 **报告保存**：自动生成带时间戳的检查报告文件
- 🖥️ **控制台输出**：实时显示检查进度和结果摘要

## 🚀 使用方法

### 1. 快速运行
双击 `run_duplicate_check.bat` 文件，自动检查 `H:\DailyUpdate` 目录。

### 2. 命令行运行

#### 基本用法
```bash
python check_duplicate_keys.py
```
默认检查 `H:\DailyUpdate` 目录

#### 指定目录
```bash
python check_duplicate_keys.py "D:\GameClient\Effects"
```
检查指定目录

#### 详细模式
```bash
python check_duplicate_keys.py --detail
```
在控制台显示详细的重复键名信息

#### 指定目录 + 详细模式
```bash
python check_duplicate_keys.py "D:\GameClient\Effects" --detail
```

## 📊 输出示例

### 控制台输出摘要
```
================================================================================
检查摘要
================================================================================
检查文件总数: 156
有重复键名的文件数: 3
有重复键名的光效数: 8

发现重复键名的文件:
  1. 3deffect2.ini - 5 个光效有重复键名
     [smgx1_pkyh24_zsyj]: TextureId1, ASB1
     [red0]: TextureId0
     [green0]: EffectId2, TextureId2
  2. 3deffect3.ini - 2 个光效有重复键名
     [combo0]: TextureId1_1
     [blue0]: MixOpt0
  3. custom_effects.ini - 1 个光效有重复键名
     [special_effect]: EffectId0
================================================================================
```

### 详细模式输出
```
================================================================================
详细重复键名信息
================================================================================

1. 文件: H:\DailyUpdate\ini\3deffect2.ini
   光效: [smgx1_pkyh24_zsyj]
     重复键名: TextureId1
       第45行: TextureId1=116979
       (首次出现在第42行)
       第48行: TextureId1=116980
       (首次出现在第42行)
     重复键名: ASB1
       第46行: ASB1=5
       (首次出现在第43行)
       第49行: ASB1=6
       (首次出现在第43行)
```

### 生成的报告文件
工具会自动生成名为 `重复键名检查报告_YYYYMMDD_HHMMSS.txt` 的详细报告文件，包含：

- 检查时间和统计信息
- 每个有问题文件的详细信息
- 每个光效的重复键名列表
- 重复键名的具体行号和值

## 🔧 检测原理

### 1. 文件扫描
- 递归遍历指定目录下的所有 `.ini` 文件
- 支持多层目录结构

### 2. 重复键名检测
- 首先尝试使用标准 `configparser` 解析
- 如果遇到 `DuplicateOptionError` 异常，说明存在重复键名
- 使用手动解析模式详细分析重复键名的位置和内容

### 3. 结果分析
- 按文件分组显示重复键名
- 按光效section分组显示问题
- 记录每个重复键名的行号和值

## 📋 支持的文件格式

工具支持检查以下格式的INI文件：

```ini
[光效名称1]
Amount=3
EffectId0=111070
TextureId0=116978
TextureId0=116979    # 重复键名
ASB0=5
ASB0=6               # 重复键名

[光效名称2]
EffectId0=111071
EffectId0=111072     # 重复键名
TextureId0=116980
```

## ⚠️ 注意事项

1. **编码格式**：默认使用GBK编码读取INI文件
2. **文件权限**：确保对检查目录有读取权限
3. **大文件处理**：大量文件时检查可能需要较长时间
4. **报告文件**：报告文件保存在脚本运行目录

## 🔍 常见重复键名类型

根据光效配置的特点，常见的重复键名包括：

- **TextureId系列**：TextureId0, TextureId1, TextureId2...
- **EffectId系列**：EffectId0, EffectId1, EffectId2...
- **ASB/ADB系列**：ASB0, ASB1, ADB0, ADB1...
- **ZTest/Lev系列**：ZTest0, ZTest1, Lev0, Lev1...
- **辅助贴图**：TextureId0_1, TextureId1_1...
- **混合选项**：MixOpt0, MixOpt1...

## 📈 性能特点

- **快速扫描**：高效的文件遍历和解析
- **内存优化**：逐文件处理，避免大量内存占用
- **进度显示**：实时显示检查进度
- **错误处理**：完善的异常处理机制

## 🛠️ 故障排除

### 常见问题

**Q: 提示"目录不存在"**
A: 检查目录路径是否正确，确保目录存在且有访问权限

**Q: 检查速度很慢**
A: 可能是文件数量较多或文件较大，请耐心等待

**Q: 报告文件生成失败**
A: 检查脚本运行目录是否有写入权限

**Q: 编码错误**
A: 确保INI文件使用GBK编码保存

## 📄 输出文件

工具运行后会生成以下文件：

- `重复键名检查报告_YYYYMMDD_HHMMSS.txt` - 详细检查报告
- 控制台日志输出 - 实时检查进度和摘要

## 🎯 使用建议

1. **定期检查**：建议定期运行此工具检查光效配置
2. **备份文件**：修复重复键名前建议备份原文件
3. **详细模式**：首次使用建议开启详细模式了解问题详情
4. **报告分析**：仔细查看生成的报告文件，了解具体问题位置

## 📋 系统要求

- Python 3.7+
- 标准库模块：configparser, pathlib, logging
- 对检查目录的读取权限
- 对脚本运行目录的写入权限（生成报告）

---

**工具版本**：1.0
**适用场景**：游戏光效配置文件质量检查
**开发语言**：Python
