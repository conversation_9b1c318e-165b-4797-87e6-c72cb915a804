# 光效资源提取工具 - 使用示例

## 🎯 新功能演示

### 1. 配置保存功能

提取光效时，工具会自动在输出目录中保存完整的光效配置文件。

#### 输出示例
```
输出目录/
└── 客户端更新/
    └── smgx1_pkyh24_zsyj/
        ├── 1.c3                    # 模型文件
        ├── 2.c3
        ├── 3.c3
        ├── 4.c3
        ├── 5.c3
        ├── 6.c3
        ├── 7.c3
        ├── 1.png                   # 贴图文件
        ├── 2.png
        ├── 3.png
        ├── 4.png
        ├── 5.png
        ├── 6.png
        ├── 7.png
        ├── 6_1.png                 # 辅助贴图
        ├── 7_1.png
        └── smgx1_pkyh24_zsyj.ini   # 🆕 光效配置文件
```

#### 配置文件内容示例
```ini
[smgx1_pkyh24_zsyj]
Amount=7
EffectId0=111070
TextureId0=116978
ASB0=5
ADB0=2
ZTest0=1
Lev0=1
EffectId1=111071
TextureId1=116979
ASB1=5
ADB1=2
ZTest1=1
Lev1=1
...
EffectId5=111168
TextureId5=117093
TextureId5_1=117094
MixOpt5=28
ASB5=5
ADB5=2
ZTest5=1
Lev5=1
...
Delay=0
LoopTime=99999999
FrameInterval=33
LoopInterval=0
OffsetX=0
OffsetY=0
OffsetZ=0
ManualUVStep=0
```

### 2. 批量处理功能

#### 创建title列表文件

创建一个名为 `my_effects.txt` 的文件：

```txt
# 我的光效列表
# 这是注释行，会被忽略

# 主要光效
smgx1_pkyh24_zsyj
smgx1_pkyh24_zsgj

# 基础光效
red0
green0
combo0

# 特殊光效
19yearfortuner
20hks_starroad
22qrj_yslgx

# 空行会被自动忽略

# 更多光效可以继续添加...
```

#### 批量提取步骤

1. **启动工具**：运行 `python effect_extractor.py`
2. **选择模式**：选择"批量提取"单选按钮
3. **设置路径**：
   - 客户端根目录：`H:\DailyUpdate`
   - 输出目录：`E:\光效输出`
   - Title列表文件：`E:\my_effects.txt`
4. **开始提取**：点击"开始提取"按钮
5. **查看进度**：在日志区域实时查看处理进度

#### 批量处理报告示例

提取完成后会生成 `批量提取报告.txt`：

```
光效资源批量提取报告
==================================================

总计: 8 个光效
成功: 6 个
失败: 2 个

成功提取的光效:
------------------------------
✅ smgx1_pkyh24_zsyj (复制了 16 个文件)
✅ smgx1_pkyh24_zsgj (复制了 14 个文件)
✅ red0 (复制了 2 个文件)
✅ green0 (复制了 2 个文件)
✅ combo0 (复制了 8 个文件)
✅ 19yearfortuner (复制了 2 个文件)

提取失败的光效:
------------------------------
❌ 20hks_starroad: 未找到光效配置: 20hks_starroad
❌ nonexistent_effect: 未找到光效配置: nonexistent_effect
```

### 3. 高级参数支持

工具现在支持复杂的光效参数：

#### 支持的参数类型
- **基础参数**：Amount, EffectId, TextureId, ASB, ADB, ZTest, Lev
- **扩展参数**：TextureId_1 (辅助贴图), MixOpt (混合选项)
- **控制参数**：Delay, LoopTime, FrameInterval, LoopInterval
- **位置参数**：OffsetX, OffsetY, OffsetZ, ManualUVStep

#### 实际解析示例
```
光效名称: smgx1_pkyh24_zsyj
✅ 成功解析 7 个光效资源
✅ 找到 7 个模型文件 (.c3)
✅ 找到 9 个贴图文件 (.png，包括辅助贴图)
✅ 正确处理 TextureId_1 和 MixOpt 参数
```

### 4. 错误处理改进

#### 重复键名处理
```
2025-08-06 18:47:32,916 - WARNING - INI文件中存在重复键名 客户端\ini\3deffect2.ini: 
While reading from '客户端\\ini\\3deffect2.ini' [line 36920]: 
option 'textureid9' in section 'electric_1_999' already exists

✅ 工具自动处理重复键名，继续正常工作
```

#### 文件不存在处理
```
⚠️ 以下文件不存在:
   - 模型文件: c3/effect/interface/test_effect/1.c3
   - 贴图文件: c3/effect/interface/test_effect/1.png

✅ 工具会记录警告但不会中断处理流程
```

## 🛠️ 编程式使用

### 单个光效提取
```python
from effect_extractor import EffectExtractor

extractor = EffectExtractor()
success, result = extractor.extract_effect(
    client_path="H:/DailyUpdate",
    output_path="E:/光效输出", 
    effect_title="smgx1_pkyh24_zsyj"
)

if success:
    print(f"提取成功! 复制了 {len(result)} 个文件")
else:
    print(f"提取失败: {result}")
```

### 批量光效提取
```python
from effect_extractor import EffectExtractor

extractor = EffectExtractor()
success, result = extractor.extract_effects_from_file(
    client_path="H:/DailyUpdate",
    output_path="E:/光效输出",
    title_file_path="E:/my_effects.txt"
)

if success:
    print(f"批量提取完成!")
    print(f"成功: {len(result['success'])} 个")
    print(f"失败: {len(result['failed'])} 个")
```

## 📋 最佳实践

### 1. 准备工作
- 确保客户端目录完整，包含所有ini文件
- 创建专门的输出目录，避免文件混乱
- 准备好要提取的光效名称列表

### 2. 批量处理建议
- 将相关的光效分组到不同的txt文件中
- 使用有意义的注释来组织光效列表
- 定期备份重要的光效配置

### 3. 故障排除
- 查看详细的日志输出定位问题
- 检查光效名称是否正确（区分大小写）
- 确认客户端文件是否完整

### 4. 性能优化
- 批量处理大量光效时，建议分批进行
- 定期清理输出目录中的临时文件
- 使用SSD存储可以提高处理速度

## 🔧 自定义扩展

工具设计为模块化结构，可以轻松扩展：

### 添加新的参数类型
```python
# 在parse_effect_params方法中添加新参数
params['new_param'] = []
for i in range(params['amount']):
    new_value = effect_config.getint(f'NewParam{i}', 0)
    params['new_param'].append(new_value)
```

### 自定义输出格式
```python
# 修改copy_resources方法中的路径生成逻辑
if custom_format:
    dst_path = os.path.join(base_output_dir, 'custom', filename)
```

### 添加新的文件类型支持
```python
# 在get_resource_paths方法中添加新的资源类型
if filename.endswith('.new_format'):
    # 处理新格式文件
    pass
```
