#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实客户端配置
"""

import os
import tempfile
from effect_extractor import EffectExtractor


def test_real_client_config():
    """测试真实客户端配置解析"""
    print("=" * 60)
    print("测试真实客户端配置解析")
    print("=" * 60)
    
    # 创建提取器实例
    extractor = EffectExtractor()
    
    # 设置客户端路径
    client_path = "客户端"
    effect_title = "smgx1_pkyh24_zsyj"
    
    print(f"客户端路径: {client_path}")
    print(f"光效名称: {effect_title}")
    
    try:
        # 1. 查找光效配置
        print("\n1. 查找光效配置...")
        effect_config, found_file = extractor.find_effect_config(client_path, effect_title)
        
        if effect_config:
            print(f"✅ 在 {found_file} 中找到光效配置")
            
            # 2. 解析光效参数
            print("\n2. 解析光效参数...")
            params = extractor.parse_effect_params(effect_config)
            
            if params:
                print("✅ 参数解析成功")
                print(f"   Amount: {params['amount']}")
                print(f"   EffectIds: {params['effect_ids']}")
                print(f"   TextureIds: {params['texture_ids']}")
                print(f"   TextureIds_1: {params['texture_ids_1']}")
                print(f"   MixOpts: {params['mix_opts']}")
                print(f"   Delay: {params['delay']}")
                print(f"   LoopTime: {params['loop_time']}")
                
                # 3. 获取资源路径
                print("\n3. 获取资源路径...")
                model_paths, texture_paths = extractor.get_resource_paths(
                    client_path, params['effect_ids'], params['texture_ids'], params['texture_ids_1']
                )
                
                print(f"✅ 找到 {len(model_paths)} 个模型文件:")
                for i, path in enumerate(model_paths, 1):
                    print(f"   {i}. {path}")
                
                print(f"✅ 找到 {len(texture_paths)} 个贴图文件:")
                for i, path in enumerate(texture_paths, 1):
                    print(f"   {i}. {path}")
                
                # 4. 检查文件是否存在
                print("\n4. 检查文件是否存在...")
                missing_files = []
                
                for path in model_paths:
                    full_path = os.path.join(client_path, path)
                    if not os.path.exists(full_path):
                        missing_files.append(f"模型文件: {path}")
                
                for path in texture_paths:
                    full_path = os.path.join(client_path, path)
                    if not os.path.exists(full_path):
                        missing_files.append(f"贴图文件: {path}")
                
                if missing_files:
                    print("⚠️ 以下文件不存在:")
                    for file in missing_files:
                        print(f"   - {file}")
                else:
                    print("✅ 所有资源文件都存在")
                
            else:
                print("❌ 参数解析失败")
        else:
            print("❌ 未找到光效配置")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


def test_extraction():
    """测试完整提取流程"""
    print("\n" + "=" * 60)
    print("测试完整提取流程")
    print("=" * 60)
    
    # 创建临时输出目录
    output_dir = tempfile.mkdtemp(prefix="real_test_")
    
    try:
        # 创建提取器实例
        extractor = EffectExtractor()
        
        client_path = "客户端"
        effect_title = "smgx1_pkyh24_zsyj"
        
        print(f"客户端路径: {client_path}")
        print(f"输出路径: {output_dir}")
        print(f"光效名称: {effect_title}")
        
        # 执行提取
        print("\n开始提取...")
        success, result = extractor.extract_effect(
            client_path, output_dir, effect_title
        )
        
        if success:
            print("✅ 提取成功!")
            print(f"复制的文件数量: {len(result)}")
            print("复制的文件列表:")
            for i, file_path in enumerate(result, 1):
                print(f"  {i}. {file_path}")
        else:
            print(f"❌ 提取失败: {result}")
    
    except Exception as e:
        print(f"❌ 提取过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n输出目录: {output_dir}")
        print("请手动检查输出结果")


if __name__ == "__main__":
    test_real_client_config()
    
    # 询问是否进行完整提取测试
    choice = input("\n是否进行完整提取测试? (y/n): ").strip().lower()
    if choice == 'y':
        test_extraction()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
